<template>
  <div class="data-warehouse-page">
    <div class="warehouse-content">
      <t-tabs v-model="activeTab" placement="top">
        <!-- 员工状态总览 -->
        <t-tab-panel value="employee-status" label="👥 员工状态">
          <div class="tab-content">
            <div class="status-header">
              <div class="header-top">
                <h3>员工状态总览</h3>
                <t-button theme="primary" @click="showAddEmployeeDialog = true">
                  <template #icon><span>➕</span></template>
                  添加员工
                </t-button>
              </div>
              <div class="status-summary">
                <div class="summary-item">
                  <span class="summary-label">总购物金:</span>
                  <span class="summary-value positive">¥{{ formatNumber(totalCalculatedShoppingGold) }}</span>
                </div>
                <div class="summary-item">
                  <span class="summary-label">待收回现金:</span>
                  <span class="summary-value warning">¥{{ formatNumber(totalCalculatedPendingCashback) }}</span>
                </div>
                <div class="summary-item">
                  <span class="summary-label">待收回佣金:</span>
                  <span class="summary-value warning">¥{{ formatNumber(totalCalculatedPendingCommission) }}</span>
                </div>
              </div>
            </div>

            <div class="employee-status-table">
              <t-table
                :data="calculatedEmployeeStatuses"
                :columns="employeeStatusColumns"
                row-key="employeeId"
                stripe
                hover
                max-height="500"
              />
            </div>
          </div>
        </t-tab-panel>

        <!-- 交易成本事实表 -->
        <t-tab-panel value="cost-facts" label="💰 交易成本">
          <div class="tab-content">
            <div class="fact-header">
              <h3>交易成本事实表</h3>
              <t-button theme="primary" @click="showAddCostDialog = true">
                <template #icon><span>➕</span></template>
                添加成本记录
              </t-button>
            </div>

            <div class="fact-table">
              <t-table
                :data="costFacts"
                :columns="costFactColumns"
                row-key="factId"
                stripe
                hover
                max-height="500"
              />
            </div>
          </div>
        </t-tab-panel>

        <!-- 交易收入事实表 -->
        <t-tab-panel value="revenue-facts" label="💎 交易收入">
          <div class="tab-content">
            <div class="fact-header">
              <h3>交易收入事实表</h3>
              <t-button theme="primary" @click="showAddRevenueDialog = true">
                <template #icon><span>➕</span></template>
                添加收入记录
              </t-button>
            </div>

            <div class="fact-table">
              <t-table
                :data="revenueFacts"
                :columns="revenueFactColumns"
                row-key="factId"
                stripe
                hover
                max-height="500"
              />
            </div>
          </div>
        </t-tab-panel>
      </t-tabs>
    </div>

    <!-- 添加成本记录对话框 -->
    <t-dialog
      v-model:visible="showAddCostDialog"
      header="添加交易成本记录"
      width="500px"
      :footer="false"
    >
      <div class="dialog-content">
        <t-form
          :data="costForm"
          label-width="120px"
          @submit="addCostRecord"
        >
          <t-form-item label="员工">
            <t-select v-model="costForm.employeeId" placeholder="请选择员工">
              <t-option
                v-for="employee in availableEmployees"
                :key="employee.employeeId"
                :value="employee.employeeId"
                :label="employee.employeeName"
              />
            </t-select>
          </t-form-item>
          
          <t-form-item label="本周支付">
            <t-input-number
              v-model="costForm.weeklyPayment"
              :decimal-places="2"
              :step="0.01"
              :min="0"
              placeholder="请输入本周支付金额"
            />
          </t-form-item>
        </t-form>
        
        <div class="dialog-actions">
          <t-button variant="outline" @click="showAddCostDialog = false">取消</t-button>
          <t-button theme="primary" @click="addCostRecord">确定</t-button>
        </div>
      </div>
    </t-dialog>

    <!-- 添加收入记录对话框 -->
    <t-dialog
      v-model:visible="showAddRevenueDialog"
      header="添加交易收入记录"
      width="500px"
      :footer="false"
    >
      <div class="dialog-content">
        <t-form
          :data="revenueForm"
          label-width="120px"
          @submit="addRevenueRecord"
        >
          <t-form-item label="员工">
            <t-select v-model="revenueForm.employeeId" placeholder="请选择员工">
              <t-option
                v-for="employee in availableEmployees"
                :key="employee.employeeId"
                :value="employee.employeeId"
                :label="employee.employeeName"
              />
            </t-select>
          </t-form-item>
          
          <t-form-item label="返还现金">
            <t-input-number
              v-model="revenueForm.returnCash"
              :decimal-places="2"
              :step="0.01"
              :min="0"
              placeholder="请输入返还现金金额"
            />
          </t-form-item>
          
          <t-form-item label="返还佣金">
            <t-input-number
              v-model="revenueForm.returnCommission"
              :decimal-places="2"
              :step="0.01"
              :min="0"
              placeholder="请输入返还佣金金额"
            />
          </t-form-item>
        </t-form>
        
        <div class="dialog-actions">
          <t-button variant="outline" @click="showAddRevenueDialog = false">取消</t-button>
          <t-button theme="primary" @click="addRevenueRecord">确定</t-button>
        </div>
      </div>
    </t-dialog>

    <!-- 编辑员工信息对话框 -->
    <t-dialog
      v-model:visible="showEditEmployeeDialog"
      header="编辑员工信息"
      width="600px"
      :footer="false"
      class="modern-edit-dialog"
    >
      <div class="edit-dialog-content">
        <!-- 简化的员工信息头部 -->
        <div class="employee-header">
          <div class="employee-avatar-compact">
            <span class="avatar-text">{{ editEmployeeForm.employeeName.charAt(0) }}</span>
          </div>
          <div class="employee-basic-info">
            <h3 class="employee-name-compact">{{ editEmployeeForm.employeeName }}</h3>
            <div class="employee-stats-compact">
              <span class="stat-item">{{ editEmployeeForm.employeeCode }}</span>
              <span class="stat-divider">•</span>
              <span class="stat-item">本周 ¥{{ formatNumber(editEmployeeForm.currentWeekPayment) }}</span>
            </div>
          </div>
        </div>

        <!-- 紧凑的编辑表单 -->
        <t-form
          :data="editEmployeeForm"
          :rules="editEmployeeRules"
          ref="editEmployeeFormRef"
          layout="vertical"
          class="compact-form"
        >
          <!-- 基本信息区域 -->
          <div class="form-section">
            <div class="section-title">
              <span class="section-icon">👤</span>
              <span>基本信息</span>
            </div>
            <div class="form-fields">
              <t-form-item label="员工姓名" name="employeeName" class="compact-form-item">
                <t-input
                  v-model="editEmployeeForm.employeeName"
                  placeholder="请输入员工姓名"
                  size="medium"
                  class="compact-input"
                />
              </t-form-item>
            </div>
          </div>

          <!-- 财务信息区域 -->
          <div class="form-section">
            <div class="section-title">
              <span class="section-icon">💰</span>
              <span>财务信息</span>
            </div>

            <!-- 可编辑的财务状态卡片 -->
            <div class="financial-status-cards">
              <!-- 购物金 - 可编辑 -->
              <div
                class="status-card positive editable"
                @dblclick="startEditing('currentShoppingGold')"
                :class="{ editing: editingStates.currentShoppingGold }"
              >
                <div class="status-label">购物金</div>
                <div v-if="!editingStates.currentShoppingGold" class="status-value">
                  ¥{{ formatNumber(editEmployeeForm.currentShoppingGold) }}
                </div>
                <t-input
                  v-else
                  v-model="tempValues.currentShoppingGold"
                  placeholder="输入金额"
                  size="small"
                  class="edit-input"
                  @blur="saveEdit('currentShoppingGold')"
                  @keyup.enter="saveEdit('currentShoppingGold')"
                  @keyup.escape="cancelEdit('currentShoppingGold')"
                  ref="currentShoppingGoldInput"
                />
              </div>

              <!-- 待收现金 - 可编辑 -->
              <div
                class="status-card warning editable"
                @dblclick="startEditing('pendingCashback')"
                :class="{ editing: editingStates.pendingCashback }"
              >
                <div class="status-label">待收现金</div>
                <div v-if="!editingStates.pendingCashback" class="status-value">
                  ¥{{ formatNumber(editEmployeeForm.pendingCashback) }}
                </div>
                <t-input
                  v-else
                  v-model="tempValues.pendingCashback"
                  placeholder="输入金额"
                  size="small"
                  class="edit-input"
                  @blur="saveEdit('pendingCashback')"
                  @keyup.enter="saveEdit('pendingCashback')"
                  @keyup.escape="cancelEdit('pendingCashback')"
                  ref="pendingCashbackInput"
                />
              </div>

              <!-- 待收佣金 - 可编辑 -->
              <div
                class="status-card warning editable"
                @dblclick="startEditing('pendingCommission')"
                :class="{ editing: editingStates.pendingCommission }"
              >
                <div class="status-label">待收佣金</div>
                <div v-if="!editingStates.pendingCommission" class="status-value">
                  ¥{{ formatNumber(editEmployeeForm.pendingCommission) }}
                </div>
                <t-input
                  v-else
                  v-model="tempValues.pendingCommission"
                  placeholder="输入金额"
                  size="small"
                  class="edit-input"
                  @blur="saveEdit('pendingCommission')"
                  @keyup.enter="saveEdit('pendingCommission')"
                  @keyup.escape="cancelEdit('pendingCommission')"
                  ref="pendingCommissionInput"
                />
              </div>
            </div>
          </div>
        </t-form>

        <!-- 操作按钮 -->
        <div class="dialog-actions">
          <t-button variant="outline" size="medium" @click="closeEditDialog" class="action-btn">
            关闭
          </t-button>
        </div>
      </div>
    </t-dialog>

    <!-- 添加员工对话框 -->
    <t-dialog
      v-model:visible="showAddEmployeeDialog"
      header="添加员工"
      width="480px"
      :footer="false"
    >
      <div class="dialog-content">
        <t-form
          :data="addEmployeeForm"
          :rules="addEmployeeRules"
          ref="addEmployeeFormRef"
          label-width="100px"
          @submit="addEmployee"
        >
          <t-form-item label="员工姓名" name="employeeName">
            <t-input
              v-model="addEmployeeForm.employeeName"
              placeholder="请输入员工姓名"
              clearable
            />
          </t-form-item>

          <t-form-item label="初始购物金">
            <t-input-number
              v-model="addEmployeeForm.initialShoppingGold"
              :decimal-places="2"
              :step="0.01"
              :min="0"
              placeholder="请输入初始购物金（可选）"
            />
          </t-form-item>
        </t-form>

        <div class="dialog-actions">
          <t-button variant="outline" @click="showAddEmployeeDialog = false">取消</t-button>
          <t-button theme="primary" @click="addEmployee">确定</t-button>
        </div>
      </div>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { useEmployeeStatusStore } from '../stores/employeeStatusStore';
import {
  TransactionCostFactService,
  TransactionRevenueFactService,
  EmployeeDimensionService,
  TimeDimensionService
} from '../services/dataWarehouseService';
import { AccountService } from '../services/dataService';
import type { 
  TransactionCostFact, 
  TransactionRevenueFact, 
  EmployeeDimension 
} from '../vite-env.d.ts';

// 使用员工状态存储
const employeeStatusStore = useEmployeeStatusStore();

// 响应式数据
const activeTab = ref('employee-status');
const costFacts = ref<TransactionCostFact[]>([]);
const revenueFacts = ref<TransactionRevenueFact[]>([]);
const availableEmployees = ref<EmployeeDimension[]>([]);

// 对话框状态
const showAddCostDialog = ref(false);
const showAddRevenueDialog = ref(false);
const showEditEmployeeDialog = ref(false);
const showAddEmployeeDialog = ref(false);

// 表单数据
const costForm = ref({
  employeeId: '',
  weeklyPayment: 0
});

const revenueForm = ref({
  employeeId: '',
  returnCash: 0,
  returnCommission: 0
});

const editEmployeeForm = ref({
  employeeId: '',
  employeeName: '',
  employeeCode: '',
  currentShoppingGold: 0,
  pendingCashback: 0,
  pendingCommission: 0,
  currentWeekPayment: 0,
  shoppingGoldAdjustment: 0,
  returnCash: 0,
  returnCommission: 0
});

const addEmployeeForm = ref({
  employeeName: '',
  initialShoppingGold: 0
});

// 编辑状态管理
const editingStates = ref({
  currentShoppingGold: false,
  pendingCashback: false,
  pendingCommission: false
});

const tempValues = ref({
  currentShoppingGold: '',
  pendingCashback: '',
  pendingCommission: ''
});

// 表单验证规则
const editEmployeeRules = {
  employeeName: [
    { required: true, message: '请输入员工姓名', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ]
};

const addEmployeeRules = {
  employeeName: [
    { required: true, message: '请输入员工姓名', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ]
};

const editEmployeeFormRef = ref();
const addEmployeeFormRef = ref();

// 格式化数字
const formatNumber = (num: number) => {
  return num.toFixed(2);
};

// 获取当前时间ID
const getCurrentTimeId = () => {
  const now = new Date();
  return now.toISOString().split('T')[0]; // YYYY-MM-DD格式
};

// 计算待收回现金（根据本周支付现金的阶梯）
const calculatePendingCashback = (currentWeekPayment: number): number => {
  if (currentWeekPayment < 200) {
    return 0;
  } else if (currentWeekPayment >= 200 && currentWeekPayment < 300) {
    return 100;
  } else if (currentWeekPayment >= 300 && currentWeekPayment < 500) {
    return 200;
  } else if (currentWeekPayment >= 500) {
    return 300;
  }
  return 0;
};

// 获取当前周次
const getCurrentWeek = () => {
  const now = new Date();
  const year = now.getFullYear();
  const startOfYear = new Date(year, 0, 1);
  const days = Math.floor((now.getTime() - startOfYear.getTime()) / (24 * 60 * 60 * 1000));
  const weekNumber = Math.ceil((days + startOfYear.getDay() + 1) / 7);
  return `${year}-${weekNumber.toString().padStart(2, '0')}`;
};

// 基于事实表计算员工状态
const calculatedEmployeeStatuses = computed(() => {
  const currentWeek = getCurrentWeek();

  return availableEmployees.value.map(employee => {
    // 从成本事实表获取本周支付数据
    const costFacts = TransactionCostFactService.getByEmployeeId(employee.employeeId);
    const currentWeekCosts = costFacts.filter(fact => {
      // 简化：使用今天的日期作为当前周的判断
      const factDate = new Date(fact.timeId);
      const now = new Date();
      const diffTime = Math.abs(now.getTime() - factDate.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return diffDays <= 7; // 最近7天内的记录
    });

    const currentWeekPayment = currentWeekCosts.reduce((sum, fact) => sum + fact.weeklyPayment, 0);

    // 从收入事实表获取返还数据
    const revenueFacts = TransactionRevenueFactService.getByEmployeeId(employee.employeeId);
    const totalReturnCash = revenueFacts.reduce((sum, fact) => sum + fact.returnCash, 0);
    const totalReturnCommission = revenueFacts.reduce((sum, fact) => sum + fact.returnCommission, 0);

    // 计算当前购物金（历史返还的总和）
    const currentShoppingGold = totalReturnCash + totalReturnCommission;

    // 计算待收回金额
    const pendingCashback = calculatePendingCashback(currentWeekPayment);
    const pendingCommission = currentWeekPayment * 0.48;

    return {
      employeeId: employee.employeeId,
      employeeName: employee.employeeName,
      employeeCode: employee.employeeCode,
      currentShoppingGold,
      pendingCashback,
      pendingCommission,
      currentWeekPayment,
      lastUpdated: new Date().toISOString()
    };
  });
});

// 计算总计数据
const totalCalculatedShoppingGold = computed(() => {
  return calculatedEmployeeStatuses.value.reduce((sum, status) => sum + status.currentShoppingGold, 0);
});

const totalCalculatedPendingCashback = computed(() => {
  return calculatedEmployeeStatuses.value.reduce((sum, status) => sum + status.pendingCashback, 0);
});

const totalCalculatedPendingCommission = computed(() => {
  return calculatedEmployeeStatuses.value.reduce((sum, status) => sum + status.pendingCommission, 0);
});

// 员工状态表格列定义
const employeeStatusColumns = [
  {
    colKey: 'employeeName',
    title: '员工姓名',
    width: 120
  },
  {
    colKey: 'employeeCode',
    title: '员工编码',
    width: 100
  },
  {
    colKey: 'currentWeekPayment',
    title: '本周支付',
    width: 120,
    align: 'right' as const,
    cell: (h: any, { row }: any) => h('span', { class: 'amount' }, `¥${formatNumber(row.currentWeekPayment)}`)
  },
  {
    colKey: 'currentShoppingGold',
    title: '现有购物金',
    width: 120,
    align: 'right' as const,
    cell: (h: any, { row }: any) => h('span', { class: 'amount positive' }, `¥${formatNumber(row.currentShoppingGold)}`)
  },
  {
    colKey: 'pendingCashback',
    title: '待收回现金',
    width: 120,
    align: 'right' as const,
    cell: (h: any, { row }: any) => h('span', { class: 'amount warning' }, `¥${formatNumber(row.pendingCashback)}`)
  },
  {
    colKey: 'pendingCommission',
    title: '待收回佣金',
    width: 120,
    align: 'right' as const,
    cell: (h: any, { row }: any) => h('span', { class: 'amount warning' }, `¥${formatNumber(row.pendingCommission)}`)
  },
  {
    colKey: 'actions',
    title: '操作',
    width: 150,
    align: 'center' as const,
    cell: (h: any, { row }: any) => h('div', { style: 'display: flex; gap: 8px; justify-content: center;' }, [
      h('t-button', {
        theme: 'primary',
        variant: 'text',
        size: 'small',
        onClick: () => editEmployeeInfo(row)
      }, '编辑'),
      h('t-button', {
        theme: 'danger',
        variant: 'text',
        size: 'small',
        onClick: () => deleteEmployee(row)
      }, '删除')
    ])
  }
];

// 成本事实表列定义
const costFactColumns = [
  {
    colKey: 'factId',
    title: 'ID',
    width: 80
  },
  {
    colKey: 'employeeId',
    title: '员工ID',
    width: 100
  },
  {
    colKey: 'timeId',
    title: '时间ID',
    width: 120
  },
  {
    colKey: 'weeklyPayment',
    title: '本周支付',
    width: 120,
    align: 'right' as const,
    cell: (h: any, { row }: any) => h('span', { class: 'amount' }, `¥${formatNumber(row.weeklyPayment)}`)
  },
  {
    colKey: 'createdAt',
    title: '创建时间',
    width: 160,
    cell: (h: any, { row }: any) => new Date(row.createdAt).toLocaleString()
  }
];

// 收入事实表列定义
const revenueFactColumns = [
  {
    colKey: 'factId',
    title: 'ID',
    width: 80
  },
  {
    colKey: 'employeeId',
    title: '员工ID',
    width: 100
  },
  {
    colKey: 'timeId',
    title: '时间ID',
    width: 120
  },
  {
    colKey: 'returnCash',
    title: '返还现金',
    width: 120,
    align: 'right' as const,
    cell: (h: any, { row }: any) => h('span', { class: 'amount positive' }, `¥${formatNumber(row.returnCash)}`)
  },
  {
    colKey: 'returnCommission',
    title: '返还佣金',
    width: 120,
    align: 'right' as const,
    cell: (h: any, { row }: any) => h('span', { class: 'amount positive' }, `¥${formatNumber(row.returnCommission)}`)
  },
  {
    colKey: 'createdAt',
    title: '创建时间',
    width: 160,
    cell: (h: any, { row }: any) => new Date(row.createdAt).toLocaleString()
  }
];

// 加载数据
const loadData = () => {
  costFacts.value = TransactionCostFactService.getAll();
  revenueFacts.value = TransactionRevenueFactService.getAll();
  availableEmployees.value = EmployeeDimensionService.getAll();
};

// 添加成本记录
const addCostRecord = () => {
  if (!costForm.value.employeeId || costForm.value.weeklyPayment <= 0) {
    MessagePlugin.warning('请填写完整的成本记录信息');
    return;
  }

  try {
    TransactionCostFactService.add({
      employeeId: costForm.value.employeeId,
      timeId: getCurrentTimeId(),
      weeklyPayment: costForm.value.weeklyPayment
    });

    MessagePlugin.success('成本记录添加成功');
    showAddCostDialog.value = false;
    costForm.value = { employeeId: '', weeklyPayment: 0 };
    loadData();
  } catch (error) {
    MessagePlugin.error('添加成本记录失败');
  }
};

// 添加收入记录
const addRevenueRecord = () => {
  if (!revenueForm.value.employeeId || (revenueForm.value.returnCash <= 0 && revenueForm.value.returnCommission <= 0)) {
    MessagePlugin.warning('请填写完整的收入记录信息');
    return;
  }

  try {
    TransactionRevenueFactService.add({
      employeeId: revenueForm.value.employeeId,
      timeId: getCurrentTimeId(),
      returnCash: revenueForm.value.returnCash,
      returnCommission: revenueForm.value.returnCommission
    });

    // 更新员工状态（处理返还）
    employeeStatusStore.processReturn(
      revenueForm.value.employeeId,
      revenueForm.value.returnCash,
      revenueForm.value.returnCommission
    );

    MessagePlugin.success('收入记录添加成功');
    showAddRevenueDialog.value = false;
    revenueForm.value = { employeeId: '', returnCash: 0, returnCommission: 0 };
    loadData();
  } catch (error) {
    MessagePlugin.error('添加收入记录失败');
  }
};

// 编辑员工信息
const editEmployeeInfo = (employee: any) => {
  editEmployeeForm.value = {
    employeeId: employee.employeeId,
    employeeName: employee.employeeName,
    employeeCode: employee.employeeCode,
    currentShoppingGold: employee.currentShoppingGold,
    pendingCashback: employee.pendingCashback,
    pendingCommission: employee.pendingCommission,
    currentWeekPayment: employee.currentWeekPayment,
    shoppingGoldAdjustment: 0,
    returnCash: 0,
    returnCommission: 0
  };

  // 重置编辑状态
  editingStates.value = {
    currentShoppingGold: false,
    pendingCashback: false,
    pendingCommission: false
  };

  tempValues.value = {
    currentShoppingGold: '',
    pendingCashback: '',
    pendingCommission: ''
  };

  showEditEmployeeDialog.value = true;
};

// 开始编辑
const startEditing = (field: string) => {
  editingStates.value[field] = true;
  tempValues.value[field] = editEmployeeForm.value[field] || '';

  // 下一帧聚焦输入框
  nextTick(() => {
    const inputSelector = field === 'currentShoppingGold' ? '[ref="currentShoppingGoldInput"]' :
                         field === 'pendingCashback' ? '[ref="pendingCashbackInput"]' : '[ref="pendingCommissionInput"]';
    const input = document.querySelector(inputSelector) as HTMLInputElement;
    if (input) {
      input.focus();
      input.select();
    }
  });
};

// 保存编辑
const saveEdit = async (field: string) => {
  const newValue = parseFloat(tempValues.value[field]) || 0;
  const oldValue = editEmployeeForm.value[field];

  editEmployeeForm.value[field] = newValue;
  editingStates.value[field] = false;

  // 如果有值变化，自动保存
  if (newValue !== oldValue) {
    await autoSaveChanges(field, newValue, oldValue);
  }
};

// 取消编辑
const cancelEdit = (field: string) => {
  editingStates.value[field] = false;
  tempValues.value[field] = editEmployeeForm.value[field] || '';
};

// 自动保存变化
const autoSaveChanges = async (field: string, newValue: number, oldValue: number) => {
  try {
    let hasChanges = false;
    const operations = [];

    // 计算变化量
    const difference = newValue - oldValue;

    if (field === 'currentShoppingGold' && difference !== 0) {
      operations.push('购物金调整');
      // 添加收入事实表记录（购物金变化作为现金返还记录）
      TransactionRevenueFactService.add({
        employeeId: editEmployeeForm.value.employeeId,
        timeId: getCurrentTimeId(),
        returnCash: difference,
        returnCommission: 0
      });
      hasChanges = true;
    }

    if (field === 'pendingCashback' && difference !== 0) {
      operations.push('待收现金调整');
      // 这里可以根据业务逻辑决定如何处理待收现金的变化
      // 暂时只更新显示值，不添加事实表记录
      hasChanges = true;
    }

    if (field === 'pendingCommission' && difference !== 0) {
      operations.push('待收佣金调整');
      // 这里可以根据业务逻辑决定如何处理待收佣金的变化
      // 暂时只更新显示值，不添加事实表记录
      hasChanges = true;
    }

    if (hasChanges) {
      MessagePlugin.success(`${operations.join('、')}已保存`);
      loadData();
    }
  } catch (error: any) {
    console.error('自动保存失败:', error);
    MessagePlugin.error('保存失败，请重试');
    // 恢复原值
    editEmployeeForm.value[field] = oldValue;
  }
};

// 关闭编辑对话框
const closeEditDialog = () => {
  // 检查是否有未保存的编辑状态
  const hasUnsavedEdits = Object.values(editingStates.value).some(editing => editing);

  if (hasUnsavedEdits) {
    MessagePlugin.warning('请先完成当前编辑或按ESC取消');
    return;
  }

  showEditEmployeeDialog.value = false;
};

// 更新员工信息
const updateEmployeeInfo = async () => {
  if (!editEmployeeFormRef.value) return;

  try {
    const result = await editEmployeeFormRef.value.validate();
    if (result === true) {
      let hasChanges = false;
      const operations = [];

      // 1. 处理姓名更新
      const employees = EmployeeDimensionService.getAll();
      const currentEmployee = employees.find(emp => emp.employeeId === editEmployeeForm.value.employeeId);

      if (currentEmployee && currentEmployee.employeeName !== editEmployeeForm.value.employeeName) {
        operations.push('姓名更新');
        // 更新员工维度表
        EmployeeDimensionService.update(editEmployeeForm.value.employeeId, {
          employeeName: editEmployeeForm.value.employeeName
        });

        // 使用AccountService同步更新所有相关记录
        try {
          AccountService.update(editEmployeeForm.value.employeeId, editEmployeeForm.value.employeeName);
        } catch (error) {
          console.warn('同步更新相关记录失败:', error);
        }
        hasChanges = true;
      }

      // 2. 处理购物金调整
      if (editEmployeeForm.value.shoppingGoldAdjustment !== 0) {
        operations.push('购物金调整');
        // 添加收入事实表记录（购物金调整）
        TransactionRevenueFactService.add({
          employeeId: editEmployeeForm.value.employeeId,
          timeId: getCurrentTimeId(),
          returnCash: editEmployeeForm.value.shoppingGoldAdjustment > 0 ? editEmployeeForm.value.shoppingGoldAdjustment : 0,
          returnCommission: editEmployeeForm.value.shoppingGoldAdjustment < 0 ? Math.abs(editEmployeeForm.value.shoppingGoldAdjustment) : 0
        });
        hasChanges = true;
      }

      // 3. 处理返还操作
      if (editEmployeeForm.value.returnCash > 0 || editEmployeeForm.value.returnCommission > 0) {
        operations.push('返还处理');
        // 添加收入事实表记录
        TransactionRevenueFactService.add({
          employeeId: editEmployeeForm.value.employeeId,
          timeId: getCurrentTimeId(),
          returnCash: editEmployeeForm.value.returnCash,
          returnCommission: editEmployeeForm.value.returnCommission
        });
        hasChanges = true;
      }

      if (hasChanges) {
        MessagePlugin.success(`员工信息更新成功 (${operations.join('、')})`);
        showEditEmployeeDialog.value = false;
        loadData();
      } else {
        MessagePlugin.warning('没有检测到任何更改');
      }
    }
  } catch (error: any) {
    console.error('更新员工信息失败:', error);
    MessagePlugin.error('更新员工信息失败');
  }
};

// 添加员工
const addEmployee = async () => {
  if (!addEmployeeFormRef.value) return;

  try {
    const result = await addEmployeeFormRef.value.validate();
    if (result === true) {
      // 添加到员工维度表
      const newEmployee = EmployeeDimensionService.add({
        employeeName: addEmployeeForm.value.employeeName,
        employeeCode: '',
        status: 'active' as const
      });

      // 同时添加到账号服务（保持兼容性）
      try {
        AccountService.add(addEmployeeForm.value.employeeName);
      } catch (error) {
        console.warn('添加到账号服务失败:', error);
      }

      // 如果有初始购物金，添加到收入事实表
      if (addEmployeeForm.value.initialShoppingGold > 0) {
        TransactionRevenueFactService.add({
          employeeId: newEmployee.employeeId,
          timeId: getCurrentTimeId(),
          returnCash: addEmployeeForm.value.initialShoppingGold,
          returnCommission: 0
        });
      }

      MessagePlugin.success('员工添加成功');
      showAddEmployeeDialog.value = false;
      addEmployeeForm.value = { employeeName: '', initialShoppingGold: 0 };
      loadData();
    }
  } catch (error: any) {
    console.error('添加员工失败:', error);
    MessagePlugin.error('添加员工失败');
  }
};

// 删除员工
const deleteEmployee = async (employee: any) => {
  try {
    // 检查是否有相关的事实表记录
    const costFacts = TransactionCostFactService.getByEmployeeId(employee.employeeId);
    const revenueFacts = TransactionRevenueFactService.getByEmployeeId(employee.employeeId);

    if (costFacts.length > 0 || revenueFacts.length > 0) {
      MessagePlugin.warning('该员工有相关交易记录，无法删除');
      return;
    }

    // 从员工维度表删除
    const success = EmployeeDimensionService.delete(employee.employeeId);

    if (success) {
      // 同时从账号服务删除（保持兼容性）
      try {
        AccountService.delete(employee.employeeId);
      } catch (error) {
        console.warn('从账号服务删除失败:', error);
      }

      MessagePlugin.success('员工删除成功');
      loadData();
    } else {
      MessagePlugin.error('员工删除失败');
    }
  } catch (error: any) {
    console.error('删除员工失败:', error);
    MessagePlugin.error('删除员工失败');
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadData();
});
</script>

<style scoped>
/* 页面基础样式 */
.data-warehouse-page {
  padding: 20px;
  background: #fafbfc;
  min-height: 100vh;
}



.warehouse-content {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.tab-content {
  padding: var(--space-lg);
}

/* 状态总览样式 */
.status-header {
  margin-bottom: var(--space-lg);
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-md);
}

.status-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--gray-900);
  margin: 0;
}

.status-summary {
  display: flex;
  gap: var(--space-lg);
  padding: var(--space-md);
  background: var(--gray-50);
  border-radius: var(--radius-lg);
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.summary-label {
  font-size: 14px;
  color: var(--gray-600);
}

.summary-value {
  font-size: 18px;
  font-weight: 600;
  font-family: 'Monaco', 'Menlo', monospace;
}

.summary-value.positive {
  color: var(--success-color);
}

.summary-value.warning {
  color: var(--warning-color);
}

/* 事实表样式 */
.fact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-lg);
}

.fact-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--gray-900);
  margin: 0;
}

/* 表格样式 */
.employee-status-table,
.fact-table {
  border-radius: var(--radius-lg);
  border: 1px solid var(--gray-200);
  overflow: hidden;
}

/* 金额样式 */
.amount {
  font-family: 'Monaco', 'Menlo', monospace;
  font-weight: 600;
}

.amount.positive {
  color: var(--success-color);
}

.amount.warning {
  color: var(--warning-color);
}

/* 对话框样式 */
.dialog-content {
  padding: var(--space-md) 0;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-sm);
  margin-top: var(--space-lg);
  padding-top: var(--space-md);
  border-top: 1px solid var(--gray-200);
}

.employee-info {
  background: var(--gray-50);
  padding: var(--space-md);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-lg);
}

.employee-info h4 {
  font-size: 18px;
  font-weight: 600;
  color: var(--gray-900);
  margin: 0 0 var(--space-sm) 0;
}

.employee-info p {
  font-size: 14px;
  color: var(--gray-600);
  margin: var(--space-xs) 0;
  font-family: 'Monaco', 'Menlo', monospace;
}

/* 现代化编辑对话框样式 */
.modern-edit-dialog .t-dialog__body {
  padding: 0;
}

.edit-dialog-content {
  padding: 20px;
  background: #ffffff;
}

/* 简化的员工信息头部 */
.employee-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #e2e8f0;
}

.employee-avatar-compact {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.employee-avatar-compact .avatar-text {
  color: white;
  font-size: 16px;
  font-weight: 600;
}

.employee-basic-info {
  flex: 1;
}

.employee-name-compact {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 2px 0;
}

.employee-stats-compact {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #6b7280;
}

.stat-item {
  font-family: 'Monaco', 'Menlo', monospace;
}

.stat-divider {
  color: #d1d5db;
}

.meta-divider {
  color: #d1d5db;
}

/* 现代化表单样式 */
.modern-form {
  background: transparent;
}

.form-group {
  background: white;
  border-radius: 12px;
  margin-bottom: 16px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.form-group:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.group-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
}

.group-icon {
  font-size: 16px;
}

.group-title {
  font-size: 15px;
  font-weight: 600;
  color: #374151;
}

.group-content {
  padding: 20px;
}

/* 状态卡片 */
.status-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 24px;
}

.status-card {
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  text-align: center;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
  cursor: default;
}

.status-card:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
  transform: translateY(-1px);
}

.status-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
  font-weight: 500;
}

.status-value {
  font-size: 16px;
  font-weight: 600;
  font-family: 'Monaco', 'Menlo', monospace;
}

/* 紧凑表单样式 */
.compact-form {
  margin-top: 0;
}

.form-section {
  margin-bottom: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.section-icon {
  font-size: 14px;
}

.form-fields {
  background: #f8fafc;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 12px;
}

.form-row:last-child {
  margin-bottom: 0;
}

.compact-form-item {
  margin-bottom: 0;
}

.compact-input {
  border-radius: 6px;
}

/* 财务状态卡片 */
.financial-status-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 16px;
}

.financial-status-cards .status-card {
  padding: 12px;
  background: #ffffff;
  border-radius: 6px;
  text-align: center;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.financial-status-cards .status-card.positive {
  border-color: #10b981;
  background: #f0fdf4;
}

.financial-status-cards .status-card.warning {
  border-color: #f59e0b;
  background: #fffbeb;
}

.financial-status-cards .status-card .status-label {
  font-size: 11px;
  color: #6b7280;
  margin-bottom: 4px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.financial-status-cards .status-card .status-value {
  font-size: 14px;
  font-weight: 600;
  font-family: 'Monaco', 'Menlo', monospace;
  color: #1f2937;
}

/* 可编辑状态卡片样式 */
.financial-status-cards .status-card.editable {
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.financial-status-cards .status-card.editable::before {
  content: '双击编辑';
  position: absolute;
  top: 4px;
  right: 6px;
  font-size: 10px;
  color: #9ca3af;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.financial-status-cards .status-card.editable:hover::before {
  opacity: 1;
}

.financial-status-cards .status-card.editable:hover {
  border-color: #667eea;
  background: #f8fafc;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.financial-status-cards .status-card.editable.editing {
  border-color: #667eea;
  background: #f0f9ff;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.financial-status-cards .status-card.editable.editing::before {
  display: none;
}

.financial-status-cards .status-card .edit-input {
  width: 100%;
}

.financial-status-cards .status-card .edit-input .t-input__inner {
  text-align: center;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 13px;
  border: none;
  background: transparent;
  padding: 4px 8px;
  font-weight: 600;
}

.financial-status-cards .status-card .edit-input .t-input__inner:focus {
  box-shadow: none;
  border: none;
}



/* 对话框操作按钮 */
.dialog-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
}

.action-btn {
  min-width: 100px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.status-value.positive {
  color: #059669;
}

.status-value.warning {
  color: #d97706;
}

/* 财务操作区域 */
.financial-operations {
  margin-top: 20px;
}

.operations-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16px;
  align-items: start;
}

.operation-field {
  display: flex;
  flex-direction: column;
}

.operation-field .t-form__item {
  margin-bottom: 0;
}

.operation-field .t-form__label {
  font-size: 13px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.operation-field .t-form__controls {
  margin-top: 0;
}

.compact-input {
  width: 100% !important;
  max-width: 180px;
}

.compact-input .t-input__inner {
  text-align: center;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 13px;
}

.compact-input .t-input__inner::placeholder {
  font-family: system-ui, -apple-system, sans-serif;
  font-size: 12px;
  color: #9ca3af;
}



/* 对话框底部 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  margin-top: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 中等屏幕适配 */
@media (max-width: 1024px) and (min-width: 769px) {
  .operations-grid {
    grid-template-columns: 1fr 1fr;
    gap: 14px;
  }

  .operation-field:last-child {
    grid-column: 1 / -1;
    max-width: 50%;
  }

  .compact-input {
    max-width: 160px;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .data-warehouse-page {
    padding: var(--space-md);
  }



  .status-summary {
    flex-direction: column;
    gap: var(--space-md);
  }

  .fact-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-md);
  }

  .tab-content {
    padding: var(--space-md);
  }

  .header-top {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-md);
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .form-section {
    padding: var(--space-md);
  }

  /* 移动端优化 */
  .edit-dialog-content {
    padding: 16px;
  }

  .employee-header {
    padding: 12px;
    margin-bottom: 16px;
  }

  .employee-avatar-compact {
    width: 36px;
    height: 36px;
  }

  .employee-avatar-compact .avatar-text {
    font-size: 14px;
  }

  .employee-name-compact {
    font-size: 14px;
  }

  .employee-stats-compact {
    font-size: 11px;
  }

  .financial-status-cards {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .form-fields {
    padding: 12px;
  }

  .dialog-actions {
    flex-direction: column;
    gap: 8px;
  }

  .action-btn {
    width: 100%;
  }

  .employee-meta {
    font-size: 13px;
  }

  .status-cards {
    grid-template-columns: 1fr;
    gap: 8px;
    margin-bottom: 16px;
  }

  .status-card {
    padding: 12px;
  }

  .operations-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .compact-input {
    max-width: 100%;
  }

  .group-header {
    padding: 12px 16px;
  }

  .group-content {
    padding: 16px;
  }

  .dialog-footer {
    padding: 16px;
    margin-top: 12px;
  }
}
</style>
