<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { getCurrentWindow } from '@tauri-apps/api/window';

const appWindow = getCurrentWindow();
const isMaximized = ref(false);
const isHovered = ref(false);

// 检查窗口状态
onMounted(async () => {
  isMaximized.value = await appWindow.isMaximized();

  // 监听窗口状态变化
  const unlisten = await appWindow.onResized(() => {
    checkMaximizedState();
  });

  onUnmounted(() => {
    unlisten();
  });
});

// 检查最大化状态
const checkMaximizedState = async () => {
  isMaximized.value = await appWindow.isMaximized();
};

// 窗口控制函数
const minimizeWindow = async () => {
  await appWindow.minimize();
};

const toggleMaximize = async () => {
  if (isMaximized.value) {
    await appWindow.unmaximize();
  } else {
    await appWindow.maximize();
  }
  isMaximized.value = !isMaximized.value;
};

const closeWindow = async () => {
  await appWindow.close();
};

// 拖拽窗口
const startDrag = async () => {
  await appWindow.startDragging();
};

// 双击最大化/还原
const handleDoubleClick = async () => {
  await toggleMaximize();
};
</script>

<template>
  <div
    class="custom-titlebar"
    data-tauri-drag-region
    @mouseenter="isHovered = true"
    @mouseleave="isHovered = false"
  >
    <!-- 左侧：Logo和标题 -->
    <div class="titlebar-left" @mousedown="startDrag" @dblclick="handleDoubleClick">
      <div class="app-logo">📊</div>
      <div class="app-title">
        <span class="title-main">DataHub</span>
        <span class="title-sub">智能数据中台</span>
      </div>
    </div>

    <!-- 中间：可拖拽区域 -->
    <div class="titlebar-center" @mousedown="startDrag" @dblclick="handleDoubleClick"></div>

    <!-- 右侧：窗口控制按钮 -->
    <div class="titlebar-controls" :class="{ 'controls-visible': isHovered }">
      <button 
        class="control-btn minimize-btn" 
        @click="minimizeWindow"
        title="最小化"
      >
        <svg width="12" height="12" viewBox="0 0 12 12">
          <rect x="2" y="5" width="8" height="2" rx="1"/>
        </svg>
      </button>
      
      <button 
        class="control-btn maximize-btn" 
        @click="toggleMaximize"
        :title="isMaximized ? '还原' : '最大化'"
      >
        <svg v-if="!isMaximized" width="12" height="12" viewBox="0 0 12 12">
          <rect x="2" y="2" width="8" height="8" rx="1" fill="none" stroke="currentColor" stroke-width="1.5"/>
        </svg>
        <svg v-else width="12" height="12" viewBox="0 0 12 12">
          <rect x="2" y="3" width="6" height="6" rx="1" fill="none" stroke="currentColor" stroke-width="1.5"/>
          <rect x="4" y="1" width="6" height="6" rx="1" fill="none" stroke="currentColor" stroke-width="1.5"/>
        </svg>
      </button>
      
      <button 
        class="control-btn close-btn" 
        @click="closeWindow"
        title="关闭"
      >
        <svg width="12" height="12" viewBox="0 0 12 12">
          <path d="M2.5 2.5L9.5 9.5M9.5 2.5L2.5 9.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
        </svg>
      </button>
    </div>
  </div>
</template>

<style scoped>
.custom-titlebar {
  display: flex;
  align-items: center;
  height: 40px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  user-select: none;
  position: relative;
  z-index: 1000;
  transition: all 0.2s ease;
}

.custom-titlebar:hover {
  background: rgba(255, 255, 255, 0.98);
  border-bottom-color: rgba(0, 0, 0, 0.12);
}

.titlebar-left {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 0 16px;
  cursor: move;
}

.app-logo {
  font-size: 18px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.app-title {
  display: flex;
  flex-direction: column;
  line-height: 1;
}

.title-main {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
  letter-spacing: -0.3px;
}

.title-sub {
  font-size: 10px;
  color: #6b7280;
  font-weight: 500;
  margin-top: 1px;
}

.titlebar-center {
  flex: 1;
  height: 100%;
  cursor: move;
}

.titlebar-controls {
  display: flex;
  align-items: center;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.titlebar-controls.controls-visible,
.titlebar-controls:hover {
  opacity: 1;
}

.control-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 46px;
  height: 40px;
  border: none;
  background: transparent;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.15s ease;
  position: relative;
}

.control-btn:hover {
  background: rgba(0, 0, 0, 0.05);
  color: #374151;
}

.control-btn:active {
  background: rgba(0, 0, 0, 0.1);
  transform: scale(0.95);
}

.close-btn:hover {
  background: #ef4444;
  color: white;
}

.close-btn:active {
  background: #dc2626;
}

/* macOS 风格的控制按钮（可选） */
@media (max-width: 768px) {
  .titlebar-left {
    padding: 0 12px;
  }
  
  .app-title {
    display: none;
  }
  
  .control-btn {
    width: 40px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .custom-titlebar {
    background: rgba(31, 41, 55, 0.95);
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }
  
  .title-main {
    color: #f9fafb;
  }
  
  .title-sub {
    color: #9ca3af;
  }
  
  .control-btn {
    color: #9ca3af;
  }
  
  .control-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #f3f4f6;
  }
  
  .control-btn:active {
    background: rgba(255, 255, 255, 0.15);
  }
}
</style>
