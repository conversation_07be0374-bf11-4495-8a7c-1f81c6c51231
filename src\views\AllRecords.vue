<template>
  <div class="all-records">
    <!-- 数据概览 -->
    <div class="overview-section">
      <div class="overview-grid">
        <div class="overview-card">
          <div class="card-icon">📊</div>
          <div class="card-content">
            <h3>总记录数</h3>
            <p class="card-value">{{ allRecords.length }}</p>
            <span class="card-label">条记录</span>
          </div>
        </div>



        <div class="overview-card">
          <div class="card-icon">👥</div>
          <div class="card-content">
            <h3>活跃人员</h3>
            <p class="card-value">{{ availableEmployees.length }}</p>
            <span class="card-label">位员工</span>
          </div>
        </div>

        <div class="overview-card">
          <div class="card-icon">💰</div>
          <div class="card-content">
            <h3>总净利润</h3>
            <p class="card-value" :class="{ 'positive': totalNetProfit > 0, 'negative': totalNetProfit < 0 }">
              ¥{{ formatCurrency(totalNetProfit) }}
            </p>
            <span class="card-label">累计收益</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 详细数据选项卡 -->
    <div class="data-tabs-section">
      <t-tabs v-model="activeTab" theme="card" size="large">
        <t-tab-panel value="overview" label="📊 数据总览">
          <div class="tab-content">
            <div v-if="availableEmployees.length === 0" class="empty-state">
              <div class="empty-content">
                <div class="empty-icon">👥</div>
                <h3>暂无数据</h3>
              </div>
            </div>

            <div v-else class="employee-overview">
              <div class="overview-header">
                <h3>👥 员工数据总览</h3>
              </div>

              <div class="employee-cards">
                <div
                  v-for="employee in employeeOverviewData"
                  :key="employee.employeeId"
                  class="employee-overview-card"
                >
                  <div class="employee-card-header">
                    <div class="employee-avatar">👤</div>
                    <div class="employee-info">
                      <h4 class="employee-name">{{ employee.employeeName }}</h4>
                      <p class="employee-code">{{ employee.employeeCode }}</p>
                    </div>
                  </div>

                  <div class="employee-stats">
                    <div class="stat-row">
                      <div class="stat-item">
                        <span class="stat-label">💰 现有购物金</span>
                        <span class="stat-value positive">¥{{ formatCurrency(employee.totalShoppingGold) }}</span>
                      </div>
                    </div>

                    <div class="stat-row">
                      <div class="stat-item">
                        <span class="stat-label">📦 待收回现金</span>
                        <span class="stat-value" :class="{ 'positive': employee.pendingCashback > 0, 'neutral': employee.pendingCashback === 0 }">
                          ¥{{ formatCurrency(employee.pendingCashback) }}
                        </span>
                      </div>
                    </div>

                    <div class="stat-row">
                      <div class="stat-item">
                        <span class="stat-label">🎁 待收回佣金</span>
                        <span class="stat-value" :class="{ 'positive': employee.pendingCommission > 0, 'neutral': employee.pendingCommission === 0 }">
                          ¥{{ formatCurrency(employee.pendingCommission) }}
                        </span>
                      </div>
                    </div>

                    <div class="stat-row summary">
                      <div class="stat-item">
                        <span class="stat-label">📈 本周支付现金</span>
                        <span class="stat-value">¥{{ formatCurrency(employee.currentWeekPayment) }}</span>
                      </div>
                    </div>
                  </div>

                  <div class="employee-actions">
                    <button class="action-btn primary" @click="viewEmployeeDetails(employee.employeeId)">
                      <span class="btn-icon">📋</span>
                      查看详情
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </t-tab-panel>

        <t-tab-panel value="week" label="📅 按周查看">
          <div class="tab-content">
            <!-- 筛选器 -->
            <div class="tab-filters">
              <div class="filter-section">
                <h4 class="filter-title">周次筛选</h4>
                <div class="filter-controls">
                  <t-select
                    v-model="selectedWeek"
                    placeholder="选择周次"
                    clearable
                    @change="loadWeekRecords"
                    style="width: 200px;"
                  >
                    <t-option label="全部周次" value="" />
                    <t-option
                      v-for="week in availableWeeks"
                      :key="week"
                      :label="formatWeekLabel(week)"
                      :value="week"
                    />
                  </t-select>
                  <button class="action-btn secondary small" @click="loadWeekRecords">
                    <span class="btn-icon">🔄</span>
                    刷新
                  </button>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if="weekFilteredRecords.length === 0" class="empty-state">
              <div class="empty-content">
                <div class="empty-icon">📊</div>
                <h3>暂无数据</h3>
              </div>
            </div>

            <!-- 有数据时显示 -->
            <div v-else class="records-section">
              <!-- 周次统计 -->
              <div class="week-stats">
                <h4 class="stats-title">
                  📊 {{ selectedWeek ? formatWeekLabel(selectedWeek) : '全部周次' }} 统计
                </h4>
                <div class="stats-grid">
                  <div class="stat-item">
                    <div class="stat-icon">📝</div>
                    <div class="stat-info">
                      <span class="stat-value">{{ weekFilteredRecords.length }}</span>
                      <span class="stat-label">记录数</span>
                    </div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-icon">💰</div>
                    <div class="stat-info">
                      <span class="stat-value">¥{{ formatNumber(weekTotalCashPayment) }}</span>
                      <span class="stat-label">支付现金</span>
                    </div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-icon">🎁</div>
                    <div class="stat-info">
                      <span class="stat-value">¥{{ formatNumber(weekTotalCommission) }}</span>
                      <span class="stat-label">佣金</span>
                    </div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-icon">📈</div>
                    <div class="stat-info">
                      <span class="stat-value" :class="{ 'positive': weekTotalNetProfit > 0, 'negative': weekTotalNetProfit < 0 }">
                        ¥{{ formatNumber(weekTotalNetProfit) }}
                      </span>
                      <span class="stat-label">净利润</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 记录表格 -->
              <div class="records-table">
                <t-table
                  :data="weekFilteredRecords"
                  :columns="weekTableColumns"
                  row-key="id"
                  stripe
                  hover
                  max-height="500"
                />
              </div>
            </div>
          </div>
        </t-tab-panel>

        <t-tab-panel value="account" label="👥 按人员查看">
          <div class="tab-content">
            <!-- 筛选器 -->
            <div class="tab-filters">
              <div class="filter-section">
                <h4 class="filter-title">人员筛选</h4>
                <div class="filter-controls">
                  <t-select
                    v-model="selectedAccount"
                    placeholder="选择人员"
                    clearable
                    @change="loadAccountRecords"
                    style="width: 200px;"
                  >
                    <t-option label="全部人员" value="" />
                    <t-option
                      v-for="account in availableEmployees"
                      :key="account.id"
                      :label="account.name"
                      :value="account.id"
                    />
                  </t-select>
                  <button class="action-btn secondary small" @click="loadAccountRecords">
                    <span class="btn-icon">🔄</span>
                    刷新
                  </button>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if="accountFilteredRecords.length === 0" class="empty-state">
              <div class="empty-content">
                <div class="empty-icon">👤</div>
                <h3>暂无数据</h3>
              </div>
            </div>

            <!-- 有数据时显示 -->
            <div v-else class="records-section">
              <!-- 人员信息卡片 -->
              <div class="employee-info" v-if="selectedAccount">
                <div class="employee-card">
                  <div class="employee-avatar">👤</div>
                  <div class="employee-details">
                    <h3>{{ getSelectedAccountName() }}</h3>
                    <p>员工编码：{{ getSelectedAccountCode() }}</p>
                  </div>
                  <div class="employee-stats">
                    <div class="quick-stat">
                      <span class="quick-value">{{ accountFilteredRecords.length }}</span>
                      <span class="quick-label">总记录</span>
                    </div>
                    <div class="quick-stat">
                      <span class="quick-value" :class="{ 'positive': accountTotalNetProfit > 0, 'negative': accountTotalNetProfit < 0 }">
                        ¥{{ formatNumber(accountTotalNetProfit) }}
                      </span>
                      <span class="quick-label">总收益</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 人员统计 -->
              <div class="account-stats">
                <h4 class="stats-title">
                  📊 {{ selectedAccount ? getSelectedAccountName() : '全部人员' }} 统计
                </h4>
                <div class="stats-grid">
                  <div class="stat-item">
                    <div class="stat-icon">📝</div>
                    <div class="stat-info">
                      <span class="stat-value">{{ accountFilteredRecords.length }}</span>
                      <span class="stat-label">记录数</span>
                    </div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-icon">💰</div>
                    <div class="stat-info">
                      <span class="stat-value">¥{{ formatNumber(accountTotalCashPayment) }}</span>
                      <span class="stat-label">支付现金</span>
                    </div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-icon">🎁</div>
                    <div class="stat-info">
                      <span class="stat-value">¥{{ formatNumber(accountTotalCommission) }}</span>
                      <span class="stat-label">佣金</span>
                    </div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-icon">📈</div>
                    <div class="stat-info">
                      <span class="stat-value" :class="{ 'positive': accountTotalNetProfit > 0, 'negative': accountTotalNetProfit < 0 }">
                        ¥{{ formatNumber(accountTotalNetProfit) }}
                      </span>
                      <span class="stat-label">净利润</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 记录表格 -->
              <div class="records-table">
                <t-table
                  :data="accountFilteredRecords"
                  :columns="accountTableColumns"
                  row-key="id"
                  stripe
                  hover
                  max-height="500"
                />
              </div>
            </div>
          </div>
        </t-tab-panel>
      </t-tabs>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { AccountService } from '../services/dataService';
import {
  TransactionCostFactService,
  TransactionRevenueFactService,
  EmployeeDimensionService,
  TimeDimensionService
} from '../services/dataWarehouseService';
import { useEmployeeStatusStore } from '../stores/employeeStatusStore';
import type { WeeklyRecord, Account, TransactionCostFact, TransactionRevenueFact } from '../vite-env.d.ts';

// 基础数据
const costFacts = ref<TransactionCostFact[]>([]);
const revenueFacts = ref<TransactionRevenueFact[]>([]);
const availableEmployees = ref<Account[]>([]);
const availableWeeks = ref<string[]>([]);
const selectedWeek = ref('');
const selectedAccount = ref('');
const activeTab = ref('overview');

// 使用员工状态存储
const employeeStatusStore = useEmployeeStatusStore();

// 格式化数字
const formatNumber = (num: number) => {
  return num.toFixed(2);
};

// 计算待收回现金（根据本周支付现金的阶梯）
const calculatePendingCashback = (currentWeekPayment: number): number => {
  if (currentWeekPayment < 200) {
    return 0;
  } else if (currentWeekPayment >= 200 && currentWeekPayment < 300) {
    return 100;
  } else if (currentWeekPayment >= 300 && currentWeekPayment < 500) {
    return 200;
  } else if (currentWeekPayment >= 500) {
    return 300;
  }
  return 0;
};

// 基于事实表构建记录数据
const allRecords = computed(() => {
  const records: WeeklyRecord[] = [];

  // 从成本事实表构建记录
  costFacts.value.forEach(costFact => {
    const employee = availableEmployees.value.find(emp => emp.id === costFact.employeeId);
    if (employee) {
      // 计算相关数据
      const weeklyCommission = costFact.weeklyPayment * 0.48;
      const weeklyNetProfit = 200 - (0.52 * costFact.weeklyPayment);
      const pendingCashback = calculatePendingCashback(costFact.weeklyPayment);

      // 获取该员工的购物金（从收入事实表计算）
      const employeeRevenueFacts = revenueFacts.value.filter(rf => rf.employeeId === costFact.employeeId);
      const shoppingGold = employeeRevenueFacts.reduce((sum, rf) => sum + rf.returnCash + rf.returnCommission, 0);

      // 构建记录对象
      const record: WeeklyRecord = {
        id: costFact.factId,
        accountId: costFact.employeeId,
        accountName: employee.name,
        shoppingGold: shoppingGold,
        weeklyCashPayment: costFact.weeklyPayment,
        weeklyCommission: weeklyCommission,
        weeklyNetProfit: weeklyNetProfit,
        weekNumber: getWeekNumberFromTimeId(costFact.timeId),
        createdAt: costFact.createdAt
      };

      records.push(record);
    }
  });

  return records;
});

// 从时间ID获取周次
const getWeekNumberFromTimeId = (timeId: string): string => {
  const date = new Date(timeId);
  const year = date.getFullYear();
  const startOfYear = new Date(year, 0, 1);
  const days = Math.floor((date.getTime() - startOfYear.getTime()) / (24 * 60 * 60 * 1000));
  const weekNumber = Math.ceil((days + startOfYear.getDay() + 1) / 7);
  return `${year}-${weekNumber.toString().padStart(2, '0')}`;
};



// 总体统计
const totalNetProfit = computed(() => {
  return allRecords.value.reduce((sum, record) => sum + record.weeklyNetProfit, 0);
});

// 总计算属性（用于员工总览）
const totalCalculatedShoppingGold = computed(() => {
  return employeeOverviewData.value.reduce((sum, employee) => sum + employee.totalShoppingGold, 0);
});

const totalCalculatedPendingCashback = computed(() => {
  return employeeOverviewData.value.reduce((sum, employee) => sum + employee.pendingCashback, 0);
});

const totalCalculatedPendingCommission = computed(() => {
  return employeeOverviewData.value.reduce((sum, employee) => sum + employee.pendingCommission, 0);
});

// 获取当前周次
const getCurrentWeek = () => {
  const now = new Date();
  const year = now.getFullYear();
  const startOfYear = new Date(year, 0, 1);
  const days = Math.floor((now.getTime() - startOfYear.getTime()) / (24 * 60 * 60 * 1000));
  const weekNumber = Math.ceil((days + startOfYear.getDay() + 1) / 7);
  return `${year}-${weekNumber.toString().padStart(2, '0')}`;
};

// 员工总览数据
const employeeOverviewData = computed(() => {
  const currentWeek = getCurrentWeek();

  return availableEmployees.value.map(employee => {
    // 从成本事实表获取本周支付数据
    const employeeCostFacts = costFacts.value.filter(fact => fact.employeeId === employee.id);
    const currentWeekCosts = employeeCostFacts.filter(fact => {
      const factWeek = getWeekNumberFromTimeId(fact.timeId);
      return factWeek === currentWeek;
    });
    const currentWeekPayment = currentWeekCosts.reduce((sum, fact) => sum + fact.weeklyPayment, 0);

    // 从收入事实表获取返还数据
    const employeeRevenueFacts = revenueFacts.value.filter(fact => fact.employeeId === employee.id);
    const totalShoppingGold = employeeRevenueFacts.reduce((sum, fact) => sum + fact.returnCash + fact.returnCommission, 0);

    // 计算待收回金额
    const pendingCashback = calculatePendingCashback(currentWeekPayment);
    const pendingCommission = currentWeekPayment * 0.48;

    return {
      employeeId: employee.id,
      employeeName: employee.name,
      employeeCode: employee.employeeCode || `EMP${employee.id.padStart(3, '0')}`,
      totalShoppingGold,
      currentWeekPayment,
      pendingCashback,
      pendingCommission
    };
  });
});

// 按周过滤的记录
const weekFilteredRecords = computed(() => {
  if (!selectedWeek.value) {
    return allRecords.value;
  }
  return allRecords.value.filter(record => record.weekNumber === selectedWeek.value);
});

// 按账号过滤的记录
const accountFilteredRecords = computed(() => {
  if (!selectedAccount.value) {
    return allRecords.value;
  }
  return allRecords.value.filter(record => record.accountId === selectedAccount.value);
});

// 按周统计数据
const weekTotalCashPayment = computed(() => {
  return weekFilteredRecords.value.reduce((sum, record) => sum + record.weeklyCashPayment, 0);
});

const weekTotalCommission = computed(() => {
  return weekFilteredRecords.value.reduce((sum, record) => sum + record.weeklyCommission, 0);
});

const weekTotalNetProfit = computed(() => {
  return weekFilteredRecords.value.reduce((sum, record) => sum + record.weeklyNetProfit, 0);
});

// 按账号统计数据
const accountTotalCashPayment = computed(() => {
  return accountFilteredRecords.value.reduce((sum, record) => sum + record.weeklyCashPayment, 0);
});

const accountTotalCommission = computed(() => {
  return accountFilteredRecords.value.reduce((sum, record) => sum + record.weeklyCommission, 0);
});

const accountTotalNetProfit = computed(() => {
  return accountFilteredRecords.value.reduce((sum, record) => sum + record.weeklyNetProfit, 0);
});

// 表格列配置
const overviewTableColumns = [
  {
    colKey: 'weekNumber',
    title: '周次',
    width: 100,
    align: 'center' as const,
    sorter: true,
    cell: (h: any, { row }: any) => formatWeekLabel(row.weekNumber)
  },
  {
    colKey: 'accountName',
    title: '人员姓名',
    width: 120,
    align: 'left' as const
  },
  {
    colKey: 'weeklyCashPayment',
    title: '支付现金',
    width: 120,
    align: 'right' as const,
    sorter: true,
    cell: (h: any, { row }: any) => `¥${row.weeklyCashPayment.toFixed(2)}`
  },
  {
    colKey: 'weeklyCommission',
    title: '佣金',
    width: 120,
    align: 'right' as const,
    sorter: true,
    cell: (h: any, { row }: any) => h('span', { class: 'commission' }, `¥${row.weeklyCommission.toFixed(2)}`)
  },
  {
    colKey: 'weeklyNetProfit',
    title: '净利润',
    width: 120,
    align: 'right' as const,
    sorter: true,
    cell: (h: any, { row }: any) => {
      const value = row.weeklyNetProfit;
      return h('span', {
        class: {
          'profit': value > 0,
          'loss': value < 0
        }
      }, `¥${value.toFixed(2)}`);
    }
  },
  {
    colKey: 'createdAt',
    title: '创建时间',
    width: 160,
    align: 'center' as const,
    sorter: true,
    cell: (h: any, { row }: any) => formatDate(row.createdAt)
  }
];

const weekTableColumns = [
  {
    colKey: 'weekNumber',
    title: '周次',
    width: 100,
    align: 'center' as const,
    sorter: true,
    cell: (h: any, { row }: any) => formatWeekLabel(row.weekNumber)
  },
  {
    colKey: 'accountName',
    title: '人员姓名',
    width: 120,
    align: 'left' as const
  },
  {
    colKey: 'weeklyCashPayment',
    title: '支付现金',
    width: 120,
    align: 'right' as const,
    sorter: true,
    cell: (h: any, { row }: any) => `¥${row.weeklyCashPayment.toFixed(2)}`
  },
  {
    colKey: 'weeklyCommission',
    title: '佣金',
    width: 120,
    align: 'right' as const,
    sorter: true,
    cell: (h: any, { row }: any) => h('span', { class: 'commission' }, `¥${row.weeklyCommission.toFixed(2)}`)
  },
  {
    colKey: 'weeklyNetProfit',
    title: '净利润',
    width: 120,
    align: 'right' as const,
    sorter: true,
    cell: (h: any, { row }: any) => {
      const value = row.weeklyNetProfit;
      return h('span', {
        class: {
          'profit': value > 0,
          'loss': value < 0
        }
      }, `¥${value.toFixed(2)}`);
    }
  },
  {
    colKey: 'createdAt',
    title: '创建时间',
    width: 160,
    align: 'center' as const,
    sorter: true,
    cell: (h: any, { row }: any) => formatDate(row.createdAt)
  }
];

const accountTableColumns = [
  {
    colKey: 'weekNumber',
    title: '周次',
    width: 100,
    align: 'center' as const,
    sorter: true,
    cell: (h: any, { row }: any) => formatWeekLabel(row.weekNumber)
  },
  {
    colKey: 'weeklyCashPayment',
    title: '支付现金',
    width: 120,
    align: 'right' as const,
    sorter: true,
    cell: (h: any, { row }: any) => `¥${row.weeklyCashPayment.toFixed(2)}`
  },
  {
    colKey: 'weeklyCommission',
    title: '佣金',
    width: 120,
    align: 'right' as const,
    sorter: true,
    cell: (h: any, { row }: any) => h('span', { class: 'commission' }, `¥${row.weeklyCommission.toFixed(2)}`)
  },
  {
    colKey: 'weeklyNetProfit',
    title: '净利润',
    width: 120,
    align: 'right' as const,
    sorter: true,
    cell: (h: any, { row }: any) => {
      const value = row.weeklyNetProfit;
      return h('span', {
        class: {
          'profit': value > 0,
          'loss': value < 0
        }
      }, `¥${value.toFixed(2)}`);
    }
  },
  {
    colKey: 'createdAt',
    title: '创建时间',
    width: 160,
    align: 'center' as const,
    sorter: true,
    cell: (h: any, { row }: any) => formatDate(row.createdAt)
  }
];



// 加载记录
const loadRecords = () => {
  // 从数据仓库事实表加载数据
  costFacts.value = TransactionCostFactService.getAll();
  revenueFacts.value = TransactionRevenueFactService.getAll();
  availableEmployees.value = AccountService.getAll();

  // 从成本事实表提取可用周次
  const weeks = new Set<string>();
  costFacts.value.forEach(fact => {
    const week = getWeekNumberFromTimeId(fact.timeId);
    weeks.add(week);
  });
  availableWeeks.value = Array.from(weeks).sort();
};

// 加载按周记录
const loadWeekRecords = () => {
  // 触发重新计算
};

// 加载按账号记录
const loadAccountRecords = () => {
  // 触发重新计算
};

// 格式化周次标签
const formatWeekLabel = (weekNumber: string) => {
  const [year, week] = weekNumber.split('-');
  return `${year}年第${parseInt(week)}周`;
};

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN');
};

// 格式化数字
const formatCurrency = (num: number) => {
  if (Math.abs(num) >= 10000) {
    return (num / 10000).toFixed(1) + '万';
  }
  return num.toFixed(2);
};

// 获取选中账号名称
const getSelectedAccountName = () => {
  const account = availableEmployees.value.find(acc => acc.id === selectedAccount.value);
  return account ? account.name : '';
};

// 获取选中账号编码
const getSelectedAccountCode = () => {
  const account = availableEmployees.value.find(acc => acc.id === selectedAccount.value);
  return account ? (account.employeeCode || 'EMP' + account.id.padStart(3, '0')) : '';
};

// 查看员工详情
const viewEmployeeDetails = (employeeId: string) => {
  selectedAccount.value = employeeId;
  activeTab.value = 'account';
  loadAccountRecords();
};



onMounted(() => {
  loadRecords();
});
</script>

<style scoped>
.all-records {
  padding: 20px;
  background: #fafbfc;
  min-height: calc(100vh - 64px);
  max-width: 1200px;
  margin: 0 auto;
}

/* 页面头部 */
.page-header {
  margin-bottom: var(--space-xl);
}

.header-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.title-section {
  flex: 1;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: var(--gray-900);
  margin: 0 0 var(--space-sm) 0;
  letter-spacing: -0.5px;
}

.page-subtitle {
  font-size: 16px;
  color: var(--gray-600);
  margin: 0;
  font-weight: 400;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.header-info {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.info-text {
  font-size: 14px;
  color: var(--gray-600);
  background: var(--gray-100);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
}

/* 按钮样式 */
.action-btn {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: 12px 20px;
  border: none;
  border-radius: var(--radius-lg);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
}

.action-btn.primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: white;
  box-shadow: var(--shadow-md);
}

.action-btn.primary:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.action-btn.secondary {
  background: white;
  color: var(--gray-700);
  border: 1px solid var(--gray-200);
}

.action-btn.secondary:hover {
  background: var(--gray-50);
  border-color: var(--gray-300);
}

.action-btn.small {
  padding: 8px 12px;
  font-size: 12px;
}

.btn-icon {
  font-size: 16px;
}

/* 概览区域 */
.overview-section {
  margin-bottom: 24px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-lg);
  max-width: 900px;
  margin: 0 auto;
}

.overview-card {
  background: white;
  border-radius: var(--radius-xl);
  padding: var(--space-lg);
  border: 1px solid var(--gray-200);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--gray-300);
}

.card-icon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.card-content {
  flex: 1;
}

.card-content h3 {
  font-size: 14px;
  font-weight: 500;
  color: var(--gray-600);
  margin: 0 0 var(--space-xs) 0;
}

.card-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--gray-900);
  margin: 0 0 var(--space-xs) 0;
  letter-spacing: -0.5px;
}

.card-value.positive {
  color: var(--success-color);
}

.card-value.negative {
  color: var(--error-color);
}

.card-label {
  font-size: 12px;
  color: var(--gray-500);
}

/* 选项卡区域 */
.data-tabs-section {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  overflow: hidden;
}

/* 选项卡内容 */
.tab-content {
  padding: var(--space-lg);
  min-height: 600px;
}

/* 员工总览 */
.employee-overview {
  width: 100%;
}

.overview-header {
  margin-bottom: var(--space-xl);
  text-align: center;
}

.overview-header h3 {
  font-size: 24px;
  font-weight: 600;
  color: var(--gray-900);
  margin: 0 0 var(--space-sm) 0;
}

.overview-header p {
  font-size: 16px;
  color: var(--gray-600);
  margin: 0;
}

.employee-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--space-lg);
}

.employee-overview-card {
  background: white;
  border-radius: var(--radius-xl);
  padding: var(--space-lg);
  border: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.employee-overview-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.employee-overview-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--gray-300);
}

.employee-overview-card:hover::before {
  opacity: 1;
}

.employee-card-header {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  margin-bottom: var(--space-lg);
  padding-bottom: var(--space-md);
  border-bottom: 1px solid var(--gray-100);
}

.employee-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  flex-shrink: 0;
}

.employee-info {
  flex: 1;
}

.employee-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--gray-900);
  margin: 0 0 var(--space-xs) 0;
}

.employee-code {
  font-size: 12px;
  color: var(--gray-500);
  margin: 0;
  font-family: 'Monaco', 'Menlo', monospace;
  background: var(--gray-100);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  display: inline-block;
}

.employee-stats {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
  margin-bottom: var(--space-lg);
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-sm) 0;
}

.stat-row.summary {
  border-top: 1px solid var(--gray-100);
  padding-top: var(--space-md);
  margin-top: var(--space-sm);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.stat-label {
  font-size: 14px;
  color: var(--gray-600);
  font-weight: 500;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--gray-900);
  font-family: 'Monaco', 'Menlo', monospace;
}

.stat-value.positive {
  color: var(--success-color);
}

.stat-value.negative {
  color: var(--error-color);
}

.stat-value.neutral {
  color: var(--gray-500);
}

.employee-actions {
  display: flex;
  justify-content: center;
  gap: var(--space-sm);
}

.btn-icon {
  font-size: 16px;
}

/* 选项卡筛选器 */
.tab-filters {
  margin-bottom: var(--space-lg);
  padding-bottom: var(--space-lg);
  border-bottom: 2px solid var(--gray-100);
}

.filter-section {
  background: var(--gray-50);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
}

.filter-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--gray-800);
  margin: 0 0 var(--space-md) 0;
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

/* 总览表格 */
.overview-table {
  background: white;
  border-radius: var(--radius-lg);
  border: 1px solid var(--gray-200);
  overflow: hidden;
}

.table-header {
  padding: var(--space-lg);
  border-bottom: 1px solid var(--gray-200);
  background: var(--gray-50);
}

.table-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--gray-900);
  margin: 0 0 var(--space-xs) 0;
}

.table-header p {
  font-size: 14px;
  color: var(--gray-600);
  margin: 0;
}

/* 选项卡样式覆盖 */
:deep(.t-tabs__header) {
  background: var(--gray-50);
  border-bottom: 2px solid var(--gray-200);
  padding: 0 var(--space-lg);
}

:deep(.t-tabs__nav-item) {
  font-size: 16px;
  font-weight: 500;
  padding: var(--space-md) var(--space-lg);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  margin-right: var(--space-sm);
}

:deep(.t-tabs__nav-item--active) {
  background: white;
  border: 1px solid var(--gray-200);
  border-bottom: 2px solid white;
  color: var(--primary-color);
  font-weight: 600;
}

:deep(.t-tabs__content) {
  padding: 0;
}



.filter-section {
  background: var(--gray-50);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
}

.filter-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--gray-800);
  margin: 0 0 var(--space-md) 0;
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

/* 空状态 */
.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-content {
  text-align: center;
  padding: var(--space-xl);
}

.empty-icon {
  font-size: 64px;
  margin-bottom: var(--space-lg);
  opacity: 0.6;
}

.empty-content h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--gray-700);
  margin: 0 0 var(--space-sm) 0;
}

.empty-content p {
  font-size: 14px;
  color: var(--gray-500);
  margin: 0;
}

/* 记录区域 */
.records-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 员工信息卡片 */
.employee-info {
  margin-bottom: var(--space-lg);
}

.employee-card {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  border-radius: var(--radius-xl);
  padding: var(--space-lg);
  color: white;
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.employee-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  flex-shrink: 0;
}

.employee-details {
  flex: 1;
}

.employee-details h3 {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 var(--space-xs) 0;
}

.employee-details p {
  font-size: 14px;
  opacity: 0.8;
  margin: 0;
}

.employee-stats {
  display: flex;
  gap: var(--space-lg);
}

.quick-stat {
  text-align: center;
}

.quick-value {
  display: block;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: var(--space-xs);
}

.quick-value.positive {
  color: #a7f3d0;
}

.quick-value.negative {
  color: #fecaca;
}

.quick-label {
  font-size: 12px;
  opacity: 0.8;
}

/* 统计区域 */
.week-stats,
.account-stats {
  margin-bottom: var(--space-lg);
}

.stats-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--gray-800);
  margin: 0 0 var(--space-md) 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--space-md);
}

.stat-item {
  background: white;
  border-radius: var(--radius-lg);
  padding: var(--space-md);
  border: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  transition: all 0.2s ease;
}

.stat-item:hover {
  border-color: var(--gray-300);
  box-shadow: var(--shadow-sm);
}

.stat-icon {
  font-size: 20px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--gray-100);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-info {
  flex: 1;
}

.stat-value {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-xs);
}

.stat-value.positive {
  color: var(--success-color);
}

.stat-value.negative {
  color: var(--error-color);
}

.stat-label {
  font-size: 12px;
  color: var(--gray-500);
}

/* 表格区域 */
.records-table {
  flex: 1;
  background: white;
  border-radius: var(--radius-lg);
  border: 1px solid var(--gray-200);
  overflow: hidden;
}

/* 表格样式 */
:deep(.t-table) {
  border: none;
}

:deep(.t-table__header) {
  background: var(--gray-50);
}

:deep(.t-table__body .t-table__row:hover) {
  background: var(--gray-50);
}

.commission {
  color: var(--success-color);
  font-weight: 600;
}

.profit {
  color: var(--success-color);
  font-weight: 600;
}

.loss {
  color: var(--error-color);
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .all-records {
    padding: var(--space-md);
  }

  .header-main {
    flex-direction: column;
    gap: var(--space-md);
  }

  .header-actions {
    width: 100%;
    justify-content: center;
  }

  .overview-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .employee-card {
    flex-direction: column;
    text-align: center;
  }

  .employee-stats {
    justify-content: center;
  }

  .page-title {
    font-size: 24px;
  }

  .tab-content {
    padding: var(--space-md);
    min-height: 400px;
  }

  .employee-cards {
    grid-template-columns: 1fr;
  }

  .overview-header h3 {
    font-size: 20px;
  }

  .employee-overview-card {
    padding: var(--space-md);
  }

  .employee-card-header {
    flex-direction: column;
    text-align: center;
    gap: var(--space-sm);
  }

  .stat-row {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-xs);
  }

  .stat-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-xs);
  }

  .filter-controls {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-sm);
  }

  .filter-controls .t-select {
    width: 100% !important;
  }

  :deep(.t-tabs__nav-item) {
    font-size: 14px;
    padding: var(--space-sm) var(--space-md);
  }
}


</style>
