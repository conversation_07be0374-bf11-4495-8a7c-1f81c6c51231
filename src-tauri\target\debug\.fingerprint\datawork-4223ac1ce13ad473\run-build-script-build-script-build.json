{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[2391957752900129902, "build_script_build", false, 2165139562317983154], [12092653563678505622, "build_script_build", false, 3244915208126118416], [16702348383442838006, "build_script_build", false, 6697167894413325214]], "local": [{"RerunIfChanged": {"output": "debug\\build\\datawork-4223ac1ce13ad473\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}