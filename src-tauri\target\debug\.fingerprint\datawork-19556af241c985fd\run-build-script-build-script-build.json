{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[2391957752900129902, "build_script_build", false, 8392233052882289663], [12092653563678505622, "build_script_build", false, 7829120473756291530], [16702348383442838006, "build_script_build", false, 5048242295724665722]], "local": [{"RerunIfChanged": {"output": "debug\\build\\datawork-19556af241c985fd\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}