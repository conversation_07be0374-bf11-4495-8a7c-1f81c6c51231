{"$schema": "https://schema.tauri.app/config/2", "productName": "datawork", "version": "0.1.0", "identifier": "com.fish", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "DataHub - 智能数据中台", "width": 1000, "height": 800, "minWidth": 800, "minHeight": 600, "resizable": true, "center": true, "decorations": false, "transparent": true}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}