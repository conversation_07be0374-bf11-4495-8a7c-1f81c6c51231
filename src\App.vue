<script setup lang="ts">
import { ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import CustomTitleBar from "./components/CustomTitleBar.vue";

const router = useRouter();
const route = useRoute();

const activeValue = ref('weekly-records');

// 菜单项配置
const menuItems = [
  { value: 'weekly-records', label: '本周记录', icon: '✏️' },
  { value: 'all-records', label: '总记录', icon: '📋' },
  { value: 'reports', label: '核心报表', icon: '📈' },
  { value: 'data-warehouse', label: '数据仓库', icon: '🏢' }
];

// 监听路由变化更新活跃菜单
router.afterEach((to) => {
  if (to.name === 'WeeklyRecords') {
    activeValue.value = 'weekly-records';
  } else if (to.name === 'AllRecords') {
    activeValue.value = 'all-records';
  } else if (to.name === 'Reports') {
    activeValue.value = 'reports';
  } else if (to.name === 'DataWarehouse') {
    activeValue.value = 'data-warehouse';
  }
});

const onMenuChange = (value: string) => {
  activeValue.value = value;
  router.push(`/${value}`);
};
</script>

<template>
  <div class="app">
    <!-- 自定义窗口栏 -->
    <CustomTitleBar />

    <!-- 现代化顶部导航 -->
    <header class="modern-header">
      <div class="header-container">
        <!-- Logo区域 -->
        <div class="logo-area">
          <div class="logo-icon">📊</div>
          <div class="logo-text">
            <h1>DataHub</h1>
            <span>智能数据中台</span>
          </div>
        </div>

        <!-- 导航菜单 -->
        <nav class="nav-menu">
          <div
            v-for="item in menuItems"
            :key="item.value"
            :class="['nav-item', { active: activeValue === item.value }]"
            @click="onMenuChange(item.value)"
          >
            <div class="nav-icon">{{ item.icon }}</div>
            <span class="nav-label">{{ item.label }}</span>
          </div>
        </nav>

        <!-- 用户区域 -->
        <div class="user-area">
          <div class="user-avatar">👤</div>
          <span class="user-name">管理员</span>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-container">
      <router-view />
    </main>
  </div>
</template>

<style scoped>
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: #fafbfc;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* 现代化头部 */
.modern-header {
  background: #ffffff;
  border-bottom: 1px solid #e8eaed;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32px;
  height: 64px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Logo区域 */
.logo-area {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  font-size: 28px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.logo-text h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  color: #1a1a1a;
  letter-spacing: -0.5px;
}

.logo-text span {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

/* 导航菜单 */
.nav-menu {
  display: flex;
  gap: 8px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  color: #6b7280;
  font-weight: 500;
  font-size: 14px;
  position: relative;
  overflow: hidden;
}

.nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: -1;
}

.nav-item:hover {
  color: #374151;
  background: #f3f4f6;
  transform: translateY(-1px);
}

.nav-item.active {
  color: white;
  font-weight: 600;
}

.nav-item.active::before {
  opacity: 1;
}

.nav-icon {
  font-size: 16px;
}

.nav-label {
  white-space: nowrap;
}

/* 用户区域 */
.user-area {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  border-radius: 12px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: white;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

/* 主内容区域 */
.main-container {
  flex: 1;
  background: #fafbfc;
  min-height: calc(100vh - 104px); /* 40px titlebar + 64px header */
  overflow-y: auto;
}
</style>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Helvetica, Arial, sans-serif;
  background-color: transparent;
  line-height: 1.6;
  color: #1a1a1a;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 8px;
}

#app {
  height: 100vh;
}

/* 现代化设计系统 */
:root {
  /* 主色调 */
  --primary-color: #667eea;
  --primary-hover: #5a6fd8;
  --primary-active: #4e5bc6;

  /* 中性色 */
  --gray-50: #fafbfc;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* 功能色 */
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #3b82f6;

  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* 圆角 */
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;

  /* 间距 */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;
}

/* TDesign 主题定制 */
:root {
  --td-brand-color: var(--primary-color);
  --td-brand-color-hover: var(--primary-hover);
  --td-brand-color-active: var(--primary-active);
  --td-bg-color-page: var(--gray-50);
  --td-bg-color-container: #ffffff;
  --td-text-color-primary: var(--gray-900);
  --td-text-color-secondary: var(--gray-600);
  --td-border-level-1-color: var(--gray-200);
  --td-shadow-1: var(--shadow-sm);
  --td-shadow-2: var(--shadow-md);
  --td-radius-default: var(--radius-md);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: 3px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}

/* 选择文本样式 */
::selection {
  background: rgba(102, 126, 234, 0.2);
  color: var(--gray-900);
}

/* 焦点样式 */
:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}
</style>