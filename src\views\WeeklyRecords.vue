<template>
  <div class="weekly-records">
    <!-- 周次信息和操作栏 -->
    <div class="week-info-bar">
      <div class="week-info-content">
        <span class="week-text">{{ currentWeekInfo.weekNumber }} ({{ currentWeekInfo.startDate }} ~ {{ currentWeekInfo.endDate }})</span>
      </div>
      <div class="week-actions">
        <button
          class="modern-btn primary"
          @click="showAddDialog = true"
          :disabled="accounts.length === 0"
        >
          ➕ 添加记录
        </button>
      </div>
    </div>

    <!-- 现代化空状态 -->
    <div v-if="accounts.length === 0" class="modern-empty">
      <div class="empty-content">
        <div class="empty-icon">👥</div>
        <h3>暂无账号</h3>
        <button class="modern-btn primary" @click="$router.push('/accounts')">
          👥 前往账号管理
        </button>
      </div>
    </div>

    <div v-else-if="weeklyRecords.length === 0" class="modern-empty">
      <div class="empty-content">
        <div class="empty-icon">📝</div>
        <h3>暂无记录</h3>
        <button class="modern-btn primary" @click="showAddDialog = true">
          ➕ 添加记录
        </button>
      </div>
    </div>

    <!-- 现代化记录表格 -->
    <div v-else class="modern-table-container">
      <t-table
        :data="weeklyRecords"
        :columns="tableColumns"
        row-key="id"
        stripe
        hover
        class="modern-table"
      />
    </div>

    <!-- 现代化添加对话框 -->
    <t-dialog
      v-model:visible="showAddDialog"
      header="添加记录"
      width="500px"
      :footer="false"
      class="modern-dialog"
    >
      <div class="modern-dialog-content">
        <t-form
          :data="addForm"
          :rules="rules"
          ref="addFormRef"
          layout="vertical"
          class="modern-form"
          @submit="addRecord"
        >
          <t-form-item label="选择账号" name="accountId">
            <t-select
              v-model="addForm.accountId"
              placeholder="请选择账号"
              clearable
              size="medium"
              class="modern-input"
            >
              <t-option
                v-for="account in accounts"
                :key="account.id"
                :label="account.name"
                :value="account.id"
              />
            </t-select>
          </t-form-item>

          <t-form-item label="本周支付现金" name="weeklyCashPayment">
            <t-input-number
              v-model="addForm.weeklyCashPayment"
              :decimal-places="2"
              :step="0.01"
              :min="0"
              placeholder="请输入金额"
              @change="calculateValues"
              size="medium"
              class="modern-input"
            />
          </t-form-item>

          <div class="calculated-fields">
            <div class="calc-field">
              <label class="calc-label">本周佣金</label>
              <div class="calc-value">¥{{ calculatedCommission }}</div>
              <div class="calc-formula">支付现金 × 0.48</div>
            </div>

            <div class="calc-field">
              <label class="calc-label">每周净利润</label>
              <div class="calc-value">¥{{ calculatedNetProfit }}</div>
              <div class="calc-formula">200 - 0.52 × 支付现金</div>
            </div>
          </div>
        </t-form>

        <div class="modern-dialog-actions">
          <t-button variant="outline" @click="showAddDialog = false" class="action-btn">取消</t-button>
          <t-button theme="primary" @click="addRecord" class="action-btn primary">确定</t-button>
        </div>
      </div>
    </t-dialog>

    <!-- 编辑记录对话框 -->
    <t-dialog
      v-model:visible="showEditDialog"
      header="编辑记录"
      width="600px"
      :footer="false"
    >
      <div class="dialog-content">
        <t-form
          :data="editForm"
          :rules="rules"
          ref="editFormRef"
          label-width="140px"
          @submit="updateRecord"
        >
          <t-form-item label="账号名称">
            <t-input :value="editForm.accountName" readonly />
          </t-form-item>

          <t-form-item label="本周支付现金" name="weeklyCashPayment">
            <t-input-number
              v-model="editForm.weeklyCashPayment"
              :decimal-places="2"
              :step="0.01"
              :min="0"
              placeholder="请输入支付现金金额"
              @change="calculateEditValues"
            />
          </t-form-item>

          <t-form-item label="本周佣金">
            <t-input :value="editCalculatedCommission" readonly />
            <div class="formula-tip">
              <span class="tip-icon">📊</span>
              计算公式：本周支付现金 × 0.48
            </div>
          </t-form-item>

          <t-form-item label="每周净利润">
            <t-input :value="editCalculatedNetProfit" readonly />
            <div class="formula-tip">
              <span class="tip-icon">💰</span>
              计算公式：200 - 0.52 × 本周支付现金
            </div>
          </t-form-item>
        </t-form>

        <div class="dialog-actions">
          <t-button variant="outline" @click="showEditDialog = false">取消</t-button>
          <t-button theme="primary" @click="updateRecord">确定</t-button>
        </div>
      </div>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { AccountService, WeeklyRecordService, getCurrentWeekInfo } from '../services/dataService';
import { TransactionCostFactService } from '../services/dataWarehouseService';
import type { Account, WeeklyRecord, WeekInfo } from '../vite-env.d.ts';

const accounts = ref<Account[]>([]);
const weeklyRecords = ref<WeeklyRecord[]>([]);
const currentWeekInfo = ref<WeekInfo>({ weekNumber: '', startDate: '', endDate: '' });
const showAddDialog = ref(false);
const showEditDialog = ref(false);
const addFormRef = ref();
const editFormRef = ref();

const addForm = ref({
  accountId: '',
  weeklyCashPayment: 0
});

const editForm = ref({
  id: '',
  accountName: '',
  weeklyCashPayment: 0
});

const rules = {
  accountId: [
    { required: true, message: '请选择账号', trigger: 'change' }
  ],
  weeklyCashPayment: [
    { required: true, message: '请输入本周支付现金', trigger: 'blur' }
  ]
};

// 表格列配置
const tableColumns = [
  {
    colKey: 'accountName',
    title: '账号名称',
    width: 120,
    align: 'left' as const
  },
  {
    colKey: 'weeklyCashPayment',
    title: '本周支付现金',
    width: 140,
    align: 'right' as const,
    cell: (h: any, { row }: any) => h('span', { class: 'amount' }, `¥${row.weeklyCashPayment.toFixed(2)}`)
  },
  {
    colKey: 'weeklyCommission',
    title: '本周佣金',
    width: 120,
    align: 'right' as const,
    cell: (h: any, { row }: any) => h('span', { class: 'amount commission' }, `¥${row.weeklyCommission.toFixed(2)}`)
  },
  {
    colKey: 'weeklyNetProfit',
    title: '每周净利润',
    width: 120,
    align: 'right' as const,
    cell: (h: any, { row }: any) => {
      const value = row.weeklyNetProfit;
      return h('span', {
        class: {
          'amount': true,
          'profit': value > 0,
          'loss': value < 0
        }
      }, `¥${value.toFixed(2)}`);
    }
  },
  {
    colKey: 'createdAt',
    title: '创建时间',
    width: 180,
    align: 'center' as const,
    cell: (h: any, { row }: any) => formatDate(row.createdAt)
  },
  {
    colKey: 'operation',
    title: '操作',
    width: 150,
    align: 'center' as const,
    cell: (h: any, { row }: any) => [
      h('button', {
        class: 'action-btn secondary small',
        style: 'margin-right: 8px;',
        onClick: () => editRecord(row)
      }, [
        h('span', { class: 'btn-icon' }, '✏️'),
        h('span', '编辑')
      ]),
      h('button', {
        class: 'action-btn danger small',
        onClick: () => deleteRecord(row)
      }, [
        h('span', { class: 'btn-icon' }, '🗑️'),
        h('span', '删除')
      ])
    ]
  }
];

// 计算佣金和净利润

const calculatedCommission = computed(() => {
  return (addForm.value.weeklyCashPayment * 0.48).toFixed(2);
});

const calculatedNetProfit = computed(() => {
  return (200 - 0.52 * addForm.value.weeklyCashPayment).toFixed(2);
});



const editCalculatedCommission = computed(() => {
  return (editForm.value.weeklyCashPayment * 0.48).toFixed(2);
});

const editCalculatedNetProfit = computed(() => {
  return (200 - 0.52 * editForm.value.weeklyCashPayment).toFixed(2);
});

const calculateValues = () => {
  // 触发计算更新
};

const calculateEditValues = () => {
  // 触发计算更新
};

// 加载数据
const loadData = () => {
  accounts.value = AccountService.getAll();
  weeklyRecords.value = WeeklyRecordService.getCurrentWeekRecords();
  currentWeekInfo.value = getCurrentWeekInfo();
};

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN');
};

// 获取当前时间ID
const getCurrentTimeId = () => {
  const now = new Date();
  return now.toISOString().split('T')[0]; // YYYY-MM-DD格式
};

// 同步更新数据仓库中的成本事实表
const updateDataWarehouseCostFacts = () => {
  try {
    // 获取所有本周记录
    const allRecords = WeeklyRecordService.getAll();
    const currentWeek = getCurrentWeekInfo();

    // 筛选当前周的记录
    const currentWeekRecords = allRecords.filter(record => record.weekNumber === currentWeek.weekNumber);

    // 清除现有的成本事实表记录（当前周的）
    const existingCostFacts = TransactionCostFactService.getAll();
    const currentTimeId = getCurrentTimeId();

    // 删除今天的成本记录
    existingCostFacts.forEach(fact => {
      if (fact.timeId === currentTimeId) {
        TransactionCostFactService.delete(fact.factId);
      }
    });

    // 按员工汇总本周支付金额
    const employeePayments = new Map();
    currentWeekRecords.forEach(record => {
      const currentAmount = employeePayments.get(record.accountId) || 0;
      employeePayments.set(record.accountId, currentAmount + record.weeklyCashPayment);
    });

    // 为每个员工创建成本事实表记录
    employeePayments.forEach((totalPayment, employeeId) => {
      if (totalPayment > 0) {
        TransactionCostFactService.add({
          employeeId: employeeId,
          timeId: currentTimeId,
          weeklyPayment: totalPayment
        });
      }
    });

    console.log('数据仓库成本事实表已更新');
  } catch (error) {
    console.error('更新数据仓库失败:', error);
  }
};

// 添加记录
const addRecord = async () => {
  if (!addFormRef.value) return;

  try {
    const result = await addFormRef.value.validate();
    if (result === true) {
      WeeklyRecordService.add(
        addForm.value.accountId,
        addForm.value.weeklyCashPayment
      );

      // 添加记录后，自动更新数据仓库中的成本事实表
      updateDataWarehouseCostFacts();

      loadData();
      showAddDialog.value = false;
      resetAddForm();
      MessagePlugin.success('记录添加成功，数据仓库已同步更新');
    }
  } catch (error) {
    console.error('添加记录失败:', error);
    MessagePlugin.error('添加记录失败');
  }
};

// 重置添加表单
const resetAddForm = () => {
  addForm.value = {
    accountId: '',
    weeklyCashPayment: 0
  };
};

// 编辑记录
const editRecord = (record: WeeklyRecord) => {
  editForm.value = {
    id: record.id,
    accountName: record.accountName,
    weeklyCashPayment: record.weeklyCashPayment
  };
  showEditDialog.value = true;
};

// 更新记录
const updateRecord = async () => {
  if (!editFormRef.value) return;

  try {
    const result = await editFormRef.value.validate();
    if (result === true) {
      const success = WeeklyRecordService.update(
        editForm.value.id,
        editForm.value.weeklyCashPayment
      );
      if (success) {
        // 更新记录后，自动更新数据仓库中的成本事实表
        updateDataWarehouseCostFacts();

        loadData();
        showEditDialog.value = false;
        MessagePlugin.success('记录更新成功，数据仓库已同步更新');
      } else {
        MessagePlugin.error('记录更新失败');
      }
    }
  } catch (error) {
    console.error('更新记录失败:', error);
  }
};

// 删除记录
const deleteRecord = (record: WeeklyRecord) => {
  try {
    const success = WeeklyRecordService.delete(record.id);
    if (success) {
      // 删除记录后，同步更新数据仓库中的成本事实表
      updateDataWarehouseCostFacts();
      loadData();
      MessagePlugin.success('记录删除成功');
    } else {
      MessagePlugin.error('记录删除失败');
    }
  } catch (error) {
    console.error('删除记录失败:', error);
    MessagePlugin.error('删除记录失败');
  }
};

onMounted(() => {
  loadData();
  // 页面加载时同步数据仓库，确保数据一致性
  updateDataWarehouseCostFacts();
});
</script>

<style scoped>
.weekly-records {
  padding: 20px;
  background: #fafbfc;
  min-height: calc(100vh - 64px);
  max-width: 1200px;
  margin: 0 auto;
}

/* 周次信息栏 */
.week-info-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  margin-bottom: 24px;
}

.week-info-content {
  flex: 1;
}

.week-text {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.week-actions {
  display: flex;
  gap: 8px;
}

/* 按钮样式 */
.action-btn {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: 12px 20px;
  border: none;
  border-radius: var(--radius-lg);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-btn.primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: white;
  box-shadow: var(--shadow-md);
}

.action-btn.primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.action-btn.secondary {
  background: white;
  color: var(--gray-700);
  border: 1px solid var(--gray-200);
}

.action-btn.secondary:hover {
  background: var(--gray-50);
  border-color: var(--gray-300);
}

.action-btn.danger {
  background: white;
  color: var(--error-color);
  border: 1px solid var(--error-color);
}

.action-btn.danger:hover {
  background: var(--error-color);
  color: white;
}

.action-btn.small {
  padding: 6px 12px;
  font-size: 12px;
}

.btn-icon {
  font-size: 16px;
}



/* 空状态 */
.empty-state {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
}

.empty-content {
  text-align: center;
  padding: var(--space-xl) * 2;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: var(--space-lg);
  opacity: 0.6;
}

.empty-content h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--gray-700);
  margin: 0 0 var(--space-sm) 0;
}

.empty-content p {
  font-size: 14px;
  color: var(--gray-500);
  margin: 0 0 var(--space-lg) 0;
}

/* 记录内容 */
.records-content {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  overflow: hidden;
}

/* 表格样式 */
:deep(.t-table) {
  border: none;
}

:deep(.t-table__header) {
  background: var(--gray-50);
}

:deep(.t-table__body .t-table__row:hover) {
  background: var(--gray-50);
}

.amount {
  font-weight: 500;
  font-family: 'Monaco', 'Menlo', monospace;
}

.amount.commission {
  color: var(--success-color);
  font-weight: 600;
}

.amount.profit {
  color: var(--success-color);
  font-weight: 600;
}

.amount.loss {
  color: var(--error-color);
  font-weight: 600;
}

/* 对话框样式 */
.dialog-content {
  padding: var(--space-lg);
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-md);
  margin-top: var(--space-lg);
  padding-top: var(--space-lg);
  border-top: 1px solid var(--gray-200);
}

.formula-tip {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  margin-top: var(--space-sm);
  padding: var(--space-sm);
  background: var(--gray-50);
  border-radius: var(--radius-md);
  font-size: 12px;
  color: var(--gray-600);
}

.tip-icon {
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .weekly-records {
    padding: var(--space-md);
  }

  .header-main {
    flex-direction: column;
    gap: var(--space-md);
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .page-title {
    font-size: 24px;
  }

  .empty-content {
    padding: var(--space-xl);
  }

  .empty-icon {
    font-size: 48px;
  }
}

/* 现代化按钮样式 */
.modern-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.modern-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.modern-btn.primary {
  background: #667eea;
  color: white;
  box-shadow: 0 1px 3px rgba(102, 126, 234, 0.3);
}

.modern-btn.primary:hover:not(:disabled) {
  background: #5a6fd8;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* 现代化空状态 */
.modern-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.modern-empty .empty-content {
  text-align: center;
  padding: 40px;
}

.modern-empty .empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.modern-empty h3 {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 16px 0;
}

/* 现代化表格容器 */
.modern-table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.modern-table {
  border: none;
}

/* 现代化对话框 */
.modern-dialog .t-dialog__body {
  padding: 0;
}

.modern-dialog-content {
  padding: 24px;
}

.modern-form {
  margin-bottom: 24px;
}

.modern-input {
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.modern-input:hover {
  border-color: #667eea;
}

.modern-input:focus-within {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 计算字段 */
.calculated-fields {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-top: 16px;
}

.calc-field {
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.calc-label {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 4px;
  display: block;
}

.calc-value {
  font-size: 18px;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 4px;
  font-family: 'Monaco', 'Menlo', monospace;
}

.calc-formula {
  font-size: 11px;
  color: #9ca3af;
  font-style: italic;
}

/* 现代化对话框操作 */
.modern-dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
}

.modern-dialog-actions .action-btn {
  min-width: 80px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.modern-dialog-actions .action-btn.primary {
  background: #667eea;
  color: white;
  border: none;
  box-shadow: 0 1px 3px rgba(102, 126, 234, 0.3);
}

.modern-dialog-actions .action-btn.primary:hover {
  background: #5a6fd8;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  transform: translateY(-1px);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .calculated-fields {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .modern-dialog-actions {
    flex-direction: column;
    gap: 8px;
  }

  .modern-dialog-actions .action-btn {
    width: 100%;
  }
}
</style>
