<template>
  <div class="reports-page">
    <!-- 筛选器和操作区 -->
    <div class="filter-bar">
      <div class="filter-item">
        <t-date-range-picker
          v-model="dateRange"
          placeholder="时间范围"
          @change="onDateRangeChange"
          class="modern-filter"
          size="medium"
        />
      </div>
      <div class="filter-item">
        <t-select
          v-model="selectedEmployee"
          placeholder="选择员工"
          clearable
          @change="onEmployeeChange"
          class="modern-filter"
          size="medium"
        >
          <t-option value="" label="全部员工" />
          <t-option
            v-for="employee in employees"
            :key="employee.employeeId"
            :value="employee.employeeId"
            :label="employee.employeeName"
          />
        </t-select>
      </div>
      <!-- 操作按钮 -->
      <div class="action-buttons">
        <button class="modern-btn refresh" @click="refreshReports">
          🔄
        </button>
        <button class="modern-btn export">
          📊
        </button>
      </div>
    </div>

    <!-- 现代化指标卡片 -->
    <div class="metrics-container">
      <div class="metric-card revenue-card">
        <div class="metric-content">
          <div class="metric-icon">💰</div>
          <div class="metric-data">
            <div class="metric-value">¥{{ formatNumber(overallProfit.revenue) }}</div>
            <div class="metric-label">总收入</div>
          </div>
          <div class="metric-trend positive">+12.5%</div>
        </div>
        <div class="metric-progress">
          <div class="progress-bar revenue-bar" :style="{ width: '75%' }"></div>
        </div>
      </div>

      <div class="metric-card cost-card">
        <div class="metric-content">
          <div class="metric-icon">📦</div>
          <div class="metric-data">
            <div class="metric-value">¥{{ formatNumber(overallProfit.cost) }}</div>
            <div class="metric-label">总成本</div>
          </div>
          <div class="metric-trend negative">-3.2%</div>
        </div>
        <div class="metric-progress">
          <div class="progress-bar cost-bar" :style="{ width: '45%' }"></div>
        </div>
      </div>

      <div class="metric-card expense-card">
        <div class="metric-content">
          <div class="metric-icon">💸</div>
          <div class="metric-data">
            <div class="metric-value">¥{{ formatNumber(overallProfit.expense) }}</div>
            <div class="metric-label">总费用</div>
          </div>
          <div class="metric-trend positive">+5.8%</div>
        </div>
        <div class="metric-progress">
          <div class="progress-bar expense-bar" :style="{ width: '30%' }"></div>
        </div>
      </div>

      <div class="metric-card profit-card">
        <div class="metric-content">
          <div class="metric-icon">📈</div>
          <div class="metric-data">
            <div class="metric-value">¥{{ formatNumber(overallProfit.netProfit) }}</div>
            <div class="metric-label">净利润</div>
          </div>
          <div class="metric-trend positive">+18.7%</div>
        </div>
        <div class="metric-progress">
          <div class="progress-bar profit-bar" :style="{ width: '85%' }"></div>
        </div>
      </div>
    </div>

    <!-- 现代化报表区域 -->
    <div class="reports-section">
      <!-- 简洁标签导航 -->
      <div class="modern-tabs">
        <div
          v-for="tab in tabItems"
          :key="tab.value"
          :class="['modern-tab', { active: activeTab === tab.value }]"
          @click="onTabChange(tab.value)"
        >
          <span class="tab-icon">{{ tab.icon }}</span>
          <span class="tab-text">{{ tab.label }}</span>
        </div>
      </div>

      <!-- 报表内容容器 -->
      <div class="report-container">
        <div v-if="activeTab === 'overall'" class="report-view">
          <overall-profit-table :data="overallProfit" />
        </div>

        <div v-if="activeTab === 'employee-profit'" class="report-view">
          <employee-profit-table :data="employeeProfitStatements" />
        </div>

        <div v-if="activeTab === 'employee-expense'" class="report-view">
          <employee-expense-table :data="employeeExpenseStatements" />
        </div>

        <div v-if="activeTab === 'time-profit'" class="report-view">
          <time-profit-table
            :weekly-data="weeklyProfitStatements"
            :monthly-data="monthlyProfitStatements"
          />
        </div>

        <div v-if="activeTab === 'time-expense'" class="report-view">
          <time-expense-table
            :weekly-data="weeklyExpenseStatements"
            :monthly-data="monthlyExpenseStatements"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { ReportService } from '../services/reportService';
import { EmployeeDimensionService } from '../services/dataWarehouseService';
import type { 
  ProfitStatement, 
  EmployeeProfitStatement, 
  EmployeeExpenseStatement,
  TimeProfitStatement,
  EmployeeDimension 
} from '../vite-env.d.ts';

// 导入子组件
import OverallProfitTable from '../components/reports/OverallProfitTable.vue';
import EmployeeProfitTable from '../components/reports/EmployeeProfitTable.vue';
import EmployeeExpenseTable from '../components/reports/EmployeeExpenseTable.vue';
import TimeProfitTable from '../components/reports/TimeProfitTable.vue';
import TimeExpenseTable from '../components/reports/TimeExpenseTable.vue';

// 响应式数据
const activeTab = ref('overall');
const dateRange = ref<[string, string] | null>(null);
const selectedEmployee = ref('');
const employees = ref<EmployeeDimension[]>([]);

// 标签页配置
const tabItems = [
  { value: 'overall', label: '整体利润表', icon: '📊' },
  { value: 'employee-profit', label: '员工利润表', icon: '👥' },
  { value: 'employee-expense', label: '员工费用表', icon: '💸' },
  { value: 'time-profit', label: '时间维度利润表', icon: '📈' },
  { value: 'time-expense', label: '时间维度费用表', icon: '📉' }
];

// 报表数据
const overallProfit = ref<ProfitStatement>({
  period: '',
  revenue: 0,
  cost: 0,
  expense: 0,
  grossProfit: 0,
  netProfit: 0,
  profitMargin: 0
});

const employeeProfitStatements = ref<EmployeeProfitStatement[]>([]);
const employeeExpenseStatements = ref<EmployeeExpenseStatement[]>([]);
const weeklyProfitStatements = ref<TimeProfitStatement[]>([]);
const monthlyProfitStatements = ref<TimeProfitStatement[]>([]);
const weeklyExpenseStatements = ref<TimeProfitStatement[]>([]);
const monthlyExpenseStatements = ref<TimeProfitStatement[]>([]);

// 计算属性
const startDate = computed(() => dateRange.value?.[0]);
const endDate = computed(() => dateRange.value?.[1]);

// 方法
const loadEmployees = () => {
  employees.value = EmployeeDimensionService.getAll();
};

const loadReports = () => {
  // 加载整体利润表
  overallProfit.value = ReportService.getOverallProfitStatement(startDate.value, endDate.value);
  
  // 加载员工维度报表
  employeeProfitStatements.value = ReportService.getEmployeeProfitStatements(startDate.value, endDate.value);
  employeeExpenseStatements.value = ReportService.getEmployeeExpenseStatements(startDate.value, endDate.value);
  
  // 加载时间维度报表
  const currentYear = new Date().getFullYear();
  weeklyProfitStatements.value = ReportService.getWeeklyProfitStatements(currentYear);
  monthlyProfitStatements.value = ReportService.getMonthlyProfitStatements(currentYear);
  weeklyExpenseStatements.value = ReportService.getWeeklyExpenseStatements(currentYear);
  monthlyExpenseStatements.value = ReportService.getMonthlyExpenseStatements(currentYear);
};

const refreshReports = () => {
  loadReports();
};

const onDateRangeChange = () => {
  loadReports();
};

const onEmployeeChange = () => {
  loadReports();
};

const onTabChange = (value: string) => {
  activeTab.value = value;
};

// 格式化数字
const formatNumber = (num: number) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万';
  }
  return num.toFixed(2);
};

// 生命周期
onMounted(() => {
  loadEmployees();
  loadReports();
});
</script>

<style scoped>
.reports-page {
  padding: 20px;
  background: #fafbfc;
  min-height: calc(100vh - 64px);
  max-width: 1200px;
  margin: 0 auto;
}



.modern-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 10px;
  background: white;
  color: #6b7280;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.modern-btn:hover {
  background: #f8fafc;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.modern-btn.refresh:hover {
  color: #667eea;
}

.modern-btn.export:hover {
  color: #10b981;
}

/* 现代化筛选器和操作栏 */
.filter-bar {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  margin-bottom: 24px;
}

.filter-item {
  flex: 1;
  max-width: 200px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

.modern-filter {
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.modern-filter:hover {
  border-color: #667eea;
}

.modern-filter:focus-within {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 指标卡片网格 */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-lg);
  margin-bottom: var(--space-xl);
}

.metric-card {
  background: white;
  border-radius: var(--radius-xl);
  padding: var(--space-lg);
  border: 1px solid var(--gray-200);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--gray-300);
}

.metric-card:hover::before {
  opacity: 1;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-md);
}

.metric-icon {
  font-size: 28px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.metric-trend {
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: var(--radius-sm);
}

.metric-trend.up {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.metric-trend.down {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
}

.metric-body {
  margin-bottom: var(--space-md);
}

.metric-value {
  font-size: 28px;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--space-xs);
  letter-spacing: -0.5px;
}

.metric-label {
  font-size: 14px;
  color: var(--gray-600);
  font-weight: 500;
}

.metric-footer {
  margin-top: var(--space-md);
}

.metric-bar {
  height: 6px;
  background: var(--gray-100);
  border-radius: 3px;
  overflow: hidden;
}

.metric-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.revenue-fill {
  background: linear-gradient(90deg, var(--success-color), #059669);
}

.cost-fill {
  background: linear-gradient(90deg, var(--warning-color), #d97706);
}

.expense-fill {
  background: linear-gradient(90deg, var(--error-color), #dc2626);
}

.profit-fill {
  background: linear-gradient(90deg, var(--info-color), #2563eb);
}

/* 现代化指标容器 */
.metrics-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
}

.metrics-container .metric-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.metrics-container .metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.metric-content {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.metrics-container .metric-icon {
  font-size: 20px;
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  filter: none;
}

.revenue-card .metric-icon {
  background: #f0fdf4;
  color: #10b981;
}

.cost-card .metric-icon {
  background: #fffbeb;
  color: #f59e0b;
}

.expense-card .metric-icon {
  background: #fef2f2;
  color: #ef4444;
}

.profit-card .metric-icon {
  background: #f3f4f6;
  color: #8b5cf6;
}

.metric-data {
  flex: 1;
}

.metrics-container .metric-value {
  font-size: 20px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 2px 0;
  font-family: 'Monaco', 'Menlo', monospace;
  letter-spacing: normal;
}

.metrics-container .metric-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  margin: 0;
}

.metrics-container .metric-trend {
  font-size: 11px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 6px;
  flex-shrink: 0;
}

.metric-trend.positive {
  background: #dcfce7;
  color: #166534;
}

.metric-trend.negative {
  background: #fef2f2;
  color: #991b1b;
}

.metric-progress {
  height: 3px;
  background: #f1f5f9;
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.revenue-bar {
  background: linear-gradient(90deg, #10b981, #059669);
}

.cost-bar {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.expense-bar {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

.profit-bar {
  background: linear-gradient(90deg, #8b5cf6, #7c3aed);
}

/* 报表内容区域 */
.reports-content {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  overflow: hidden;
}

/* 标签导航 */
.tab-navigation {
  display: flex;
  background: var(--gray-50);
  border-bottom: 1px solid var(--gray-200);
  padding: var(--space-sm);
  gap: var(--space-xs);
}

.tab-item {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: 12px 20px;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 14px;
  font-weight: 500;
  color: var(--gray-600);
  background: transparent;
  border: 1px solid transparent;
  position: relative;
  overflow: hidden;
}

.tab-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: -1;
}

.tab-item:hover {
  color: var(--gray-700);
  background: white;
  border-color: var(--gray-200);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.tab-item.active {
  color: white;
  font-weight: 600;
}

.tab-item.active::before {
  opacity: 1;
}

.tab-icon {
  font-size: 16px;
}

.tab-label {
  white-space: nowrap;
}

/* 报表面板 */
.report-content {
  padding: var(--space-xl);
}

.report-panel {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .reports-page {
    padding: var(--space-md);
  }

  .header-main {
    flex-direction: column;
    gap: var(--space-md);
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .filters-section {
    flex-direction: column;
    gap: var(--space-md);
  }

  .filter-group {
    min-width: auto;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .tab-navigation {
    flex-wrap: wrap;
  }

  .tab-item {
    flex: 1;
    justify-content: center;
    min-width: 120px;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 24px;
  }

  .metric-value {
    font-size: 24px;
  }

  .report-content {
    padding: var(--space-md);
  }
}

/* 现代化报表区域样式 */
.reports-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

/* 现代化标签导航 */
.modern-tabs {
  display: flex;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  padding: 8px;
  gap: 4px;
}

.modern-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 8px;
  font-weight: 500;
  color: #6b7280;
  font-size: 14px;
  position: relative;
  background: transparent;
}

.modern-tab:hover {
  background: white;
  color: #374151;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.modern-tab.active {
  background: white;
  color: #667eea;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.modern-tab .tab-icon {
  font-size: 14px;
}

.modern-tab .tab-text {
  font-size: 13px;
  white-space: nowrap;
}

/* 报表容器 */
.report-container {
  padding: 24px;
}

.report-view {
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 响应式优化 */
@media (max-width: 768px) {
  .modern-tabs {
    flex-wrap: wrap;
    gap: 2px;
  }

  .modern-tab {
    flex: 1;
    justify-content: center;
    min-width: 100px;
    padding: 10px 12px;
  }

  .modern-tab .tab-text {
    font-size: 12px;
  }

  .metrics-container {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .filter-bar {
    flex-direction: column;
    gap: 8px;
  }

  .filter-item {
    max-width: none;
  }
}
</style>
